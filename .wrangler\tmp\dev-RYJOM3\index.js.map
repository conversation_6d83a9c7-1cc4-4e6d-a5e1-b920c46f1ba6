{"version": 3, "sources": ["../bundle-7hdGrO/strip-cf-connecting-ip-header.js", "../../../src/auth.js", "../../../src/converter.js", "../../../src/index.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-7hdGrO/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-7hdGrO/middleware-loader.entry.ts"], "sourceRoot": "E:\\Git\\sub_conventer\\.wrangler\\tmp\\dev-RYJOM3", "sourcesContent": ["function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "// 认证相关功能\n\nexport class Auth {\n  constructor(env) {\n    this.env = env;\n  }\n\n  // 验证用户凭据\n  validateCredentials(username, password) {\n    return username === this.env.ADMIN_USERNAME && password === this.env.ADMIN_PASSWORD;\n  }\n\n  // 生成JWT token (简化版)\n  generateToken(username) {\n    const payload = {\n      username,\n      exp: Date.now() + (24 * 60 * 60 * 1000) // 24小时过期\n    };\n    return btoa(JSON.stringify(payload));\n  }\n\n  // 验证token\n  validateToken(token) {\n    try {\n      const payload = JSON.parse(atob(token));\n      if (payload.exp < Date.now()) {\n        return false; // token过期\n      }\n      return payload.username === this.env.ADMIN_USERNAME;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  // 从请求中获取token\n  getTokenFromRequest(request) {\n    const cookie = request.headers.get('Cookie');\n    if (!cookie) return null;\n    \n    const match = cookie.match(/auth_token=([^;]+)/);\n    return match ? match[1] : null;\n  }\n\n  // 检查是否已认证\n  isAuthenticated(request) {\n    const token = this.getTokenFromRequest(request);\n    return token && this.validateToken(token);\n  }\n\n  // 创建认证cookie\n  createAuthCookie(token) {\n    return `auth_token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=86400; Path=/`;\n  }\n\n  // 创建登出cookie\n  createLogoutCookie() {\n    return `auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/`;\n  }\n}\n", "// 节点转换器\n\nexport class NodeConverter {\n  constructor() {}\n\n  // 解析节点链接\n  parseNode(config, type) {\n    try {\n      switch (type) {\n        case 'vmess':\n          return this.parseVmess(config);\n        case 'vless':\n          return this.parseVless(config);\n        case 'ss':\n          return this.parseShadowsocks(config);\n        case 'trojan':\n          return this.parseTrojan(config);\n        case 'http':\n          return this.parseHttp(config);\n        default:\n          throw new Error('不支持的节点类型');\n      }\n    } catch (e) {\n      throw new Error(`解析节点失败: ${e.message}`);\n    }\n  }\n\n  // 解析VMess链接\n  parseVmess(config) {\n    if (config.startsWith('vmess://')) {\n      const data = JSON.parse(atob(config.substring(8)));\n      return {\n        type: 'vmess',\n        name: data.ps || 'VMess节点',\n        server: data.add,\n        port: parseInt(data.port),\n        uuid: data.id,\n        alterId: parseInt(data.aid) || 0,\n        cipher: data.scy || 'auto',\n        network: data.net || 'tcp',\n        tls: data.tls === 'tls',\n        path: data.path || '',\n        host: data.host || ''\n      };\n    }\n    throw new Error('无效的VMess链接');\n  }\n\n  // 解析VLess链接\n  parseVless(config) {\n    if (config.startsWith('vless://')) {\n      try {\n        const url = new URL(config);\n        const params = url.searchParams;\n\n        // 基础配置\n        const node = {\n          type: 'vless',\n          name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',\n          server: url.hostname,\n          port: parseInt(url.port),\n          uuid: url.username,\n          encryption: params.get('encryption') || 'none',\n          network: params.get('type') || 'tcp'\n        };\n\n        // 验证必要字段\n        if (!node.server || !node.port || !node.uuid) {\n          throw new Error('VLess链接缺少必要参数');\n        }\n\n        // 安全传输配置\n        const security = params.get('security');\n        if (security === 'tls') {\n          node.tls = true;\n          node.sni = params.get('sni') || url.hostname;\n          node.alpn = params.get('alpn');\n          node.fingerprint = params.get('fp');\n        } else if (security === 'reality') {\n          node.reality = true;\n          node.sni = params.get('sni') || url.hostname;\n          node.fingerprint = params.get('fp') || 'chrome';\n          node.publicKey = params.get('pbk');\n          node.shortId = params.get('sid');\n          node.spiderX = params.get('spx');\n        }\n\n        // Flow控制 (XTLS)\n        const flow = params.get('flow');\n        if (flow) {\n          node.flow = flow;\n        }\n\n        // 传输协议配置\n        switch (node.network) {\n          case 'tcp':\n            node.headerType = params.get('headerType') || 'none';\n            break;\n          case 'ws':\n            node.path = params.get('path') || '/';\n            node.host = params.get('host') || '';\n            break;\n          case 'grpc':\n            node.serviceName = params.get('serviceName') || 'GunService';\n            node.mode = params.get('mode') || 'gun';\n            break;\n          case 'h2':\n            node.path = params.get('path') || '/';\n            node.host = params.get('host') || '';\n            break;\n          case 'kcp':\n            node.headerType = params.get('headerType') || 'none';\n            node.seed = params.get('seed');\n            break;\n          case 'quic':\n            node.headerType = params.get('headerType') || 'none';\n            node.quicSecurity = params.get('quicSecurity') || 'none';\n            node.key = params.get('key');\n            break;\n        }\n\n        return node;\n      } catch (error) {\n        throw new Error(`VLess链接解析失败: ${error.message}`);\n      }\n    }\n    throw new Error('无效的VLess链接');\n  }\n\n  // 解析Shadowsocks链接\n  parseShadowsocks(config) {\n    if (config.startsWith('ss://')) {\n      const url = new URL(config);\n      const userInfo = atob(url.username);\n      const [method, password] = userInfo.split(':');\n      \n      return {\n        type: 'ss',\n        name: decodeURIComponent(url.hash.substring(1)) || 'SS节点',\n        server: url.hostname,\n        port: parseInt(url.port),\n        method: method,\n        password: password\n      };\n    }\n    throw new Error('无效的Shadowsocks链接');\n  }\n\n  // 解析Trojan链接\n  parseTrojan(config) {\n    if (config.startsWith('trojan://')) {\n      const url = new URL(config);\n      return {\n        type: 'trojan',\n        name: decodeURIComponent(url.hash.substring(1)) || 'Trojan节点',\n        server: url.hostname,\n        port: parseInt(url.port),\n        password: url.username,\n        sni: url.searchParams.get('sni') || url.hostname\n      };\n    }\n    throw new Error('无效的Trojan链接');\n  }\n\n  // 解析HTTP代理\n  parseHttp(config) {\n    if (config.startsWith('http://') || config.startsWith('https://')) {\n      const url = new URL(config);\n      return {\n        type: 'http',\n        name: `HTTP-${url.hostname}`,\n        server: url.hostname,\n        port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),\n        username: url.username || '',\n        password: url.password || '',\n        tls: url.protocol === 'https:'\n      };\n    }\n    throw new Error('无效的HTTP链接');\n  }\n\n  // 转换为V2Ray订阅格式\n  toV2raySubscription(nodes) {\n    const configs = nodes.map(node => {\n      switch (node.type) {\n        case 'vmess':\n          return this.nodeToVmessLink(node);\n        case 'vless':\n          return this.nodeToVlessLink(node);\n        case 'ss':\n          return this.nodeToSsLink(node);\n        case 'trojan':\n          return this.nodeToTrojanLink(node);\n        default:\n          return null;\n      }\n    }).filter(Boolean);\n    \n    return btoa(configs.join('\\n'));\n  }\n\n  // 转换为Clash配置\n  toClashConfig(nodes) {\n    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);\n    const proxyNames = proxies.map(p => p.name);\n\n    // 如果没有有效的代理节点，创建一个基础配置\n    if (proxyNames.length === 0) {\n      return {\n        port: 7890,\n        'socks-port': 7891,\n        'allow-lan': false,\n        mode: 'rule',\n        'log-level': 'info',\n        'external-controller': '127.0.0.1:9090',\n        proxies: [],\n        'proxy-groups': [\n          {\n            name: '🎯 全球直连',\n            type: 'select',\n            proxies: ['DIRECT']\n          }\n        ],\n        rules: [\n          'DOMAIN-SUFFIX,local,DIRECT',\n          'IP-CIDR,*********/8,DIRECT',\n          'IP-CIDR,**********/12,DIRECT',\n          'IP-CIDR,***********/16,DIRECT',\n          'IP-CIDR,10.0.0.0/8,DIRECT',\n          'GEOIP,CN,🎯 全球直连',\n          'MATCH,🎯 全球直连'\n        ]\n      };\n    }\n\n    // 有代理节点时的完整配置\n    const proxyGroups = [\n      {\n        name: '🚀 节点选择',\n        type: 'select',\n        proxies: ['♻️ 自动选择', '🎯 全球直连'].concat(proxyNames)\n      },\n      {\n        name: '🎯 全球直连',\n        type: 'select',\n        proxies: ['DIRECT']\n      }\n    ];\n\n    // 只有在有多个节点时才添加自动选择组\n    if (proxyNames.length > 1) {\n      proxyGroups.splice(1, 0, {\n        name: '♻️ 自动选择',\n        type: 'url-test',\n        proxies: proxyNames,\n        url: 'http://www.gstatic.com/generate_204',\n        interval: 300,\n        tolerance: 50\n      });\n    }\n\n    return {\n      port: 7890,\n      'socks-port': 7891,\n      'allow-lan': false,\n      mode: 'rule',\n      'log-level': 'info',\n      'external-controller': '127.0.0.1:9090',\n      proxies: proxies,\n      'proxy-groups': proxyGroups,\n      rules: [\n        'DOMAIN-SUFFIX,local,DIRECT',\n        'IP-CIDR,*********/8,DIRECT',\n        'IP-CIDR,**********/12,DIRECT',\n        'IP-CIDR,***********/16,DIRECT',\n        'IP-CIDR,10.0.0.0/8,DIRECT',\n        'GEOIP,CN,🎯 全球直连',\n        'MATCH,🚀 节点选择'\n      ]\n    };\n  }\n\n  // 节点转VMess链接\n  nodeToVmessLink(node) {\n    const vmessObj = {\n      v: '2',\n      ps: node.name,\n      add: node.server,\n      port: node.port.toString(),\n      id: node.uuid,\n      aid: node.alterId.toString(),\n      scy: node.cipher,\n      net: node.network,\n      type: 'none',\n      host: node.host,\n      path: node.path,\n      tls: node.tls ? 'tls' : ''\n    };\n    return 'vmess://' + btoa(JSON.stringify(vmessObj));\n  }\n\n  // 节点转VLess链接\n  nodeToVlessLink(node) {\n    const params = new URLSearchParams();\n    params.set('encryption', node.encryption || 'none');\n    params.set('type', node.network || 'tcp');\n\n    // 安全传输配置\n    if (node.reality) {\n      params.set('security', 'reality');\n      if (node.sni) params.set('sni', node.sni);\n      if (node.fingerprint) params.set('fp', node.fingerprint);\n      if (node.publicKey) params.set('pbk', node.publicKey);\n      if (node.shortId) params.set('sid', node.shortId);\n      if (node.spiderX) params.set('spx', node.spiderX);\n    } else if (node.tls) {\n      params.set('security', 'tls');\n      if (node.sni) params.set('sni', node.sni);\n      if (node.alpn) params.set('alpn', node.alpn);\n      if (node.fingerprint) params.set('fp', node.fingerprint);\n    }\n\n    // Flow控制\n    if (node.flow) params.set('flow', node.flow);\n\n    // 传输协议配置\n    switch (node.network) {\n      case 'ws':\n        if (node.path) params.set('path', node.path);\n        if (node.host) params.set('host', node.host);\n        break;\n      case 'grpc':\n        if (node.serviceName) params.set('serviceName', node.serviceName);\n        if (node.mode) params.set('mode', node.mode);\n        break;\n      case 'h2':\n        if (node.path) params.set('path', node.path);\n        if (node.host) params.set('host', node.host);\n        break;\n      case 'tcp':\n        if (node.headerType && node.headerType !== 'none') {\n          params.set('headerType', node.headerType);\n        }\n        break;\n    }\n\n    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转SS链接\n  nodeToSsLink(node) {\n    const userInfo = btoa(`${node.method}:${node.password}`);\n    return `ss://${userInfo}@${node.server}:${node.port}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转Trojan链接\n  nodeToTrojanLink(node) {\n    const params = new URLSearchParams();\n    if (node.sni) params.set('sni', node.sni);\n    \n    return `trojan://${node.password}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转Clash代理配置\n  nodeToClashProxy(node) {\n    switch (node.type) {\n      case 'vmess':\n        const vmessProxy = {\n          name: node.name,\n          type: 'vmess',\n          server: node.server,\n          port: node.port,\n          uuid: node.uuid,\n          alterId: node.alterId || 0,\n          cipher: node.cipher || 'auto'\n        };\n\n        // 添加TLS配置\n        if (node.tls) {\n          vmessProxy.tls = true;\n        }\n\n        // 添加网络配置\n        if (node.network === 'ws') {\n          vmessProxy.network = 'ws';\n          vmessProxy['ws-opts'] = {\n            path: node.path || '/',\n            headers: node.host ? { Host: node.host } : {}\n          };\n        } else if (node.network === 'h2') {\n          vmessProxy.network = 'h2';\n          vmessProxy['h2-opts'] = {\n            host: [node.host || node.server],\n            path: node.path || '/'\n          };\n        } else {\n          vmessProxy.network = 'tcp';\n        }\n\n        return vmessProxy;\n\n      case 'vless':\n        const vlessProxy = {\n          name: node.name,\n          type: 'vless',\n          server: node.server,\n          port: node.port,\n          uuid: node.uuid,\n          'packet-encoding': 'xudp'\n        };\n\n        // 添加Flow控制 (XTLS)\n        if (node.flow) {\n          vlessProxy.flow = node.flow;\n        }\n\n        // 添加TLS配置\n        if (node.tls) {\n          vlessProxy.tls = true;\n          if (node.sni) {\n            vlessProxy.servername = node.sni;\n          }\n          if (node.alpn) {\n            vlessProxy.alpn = node.alpn.split(',');\n          }\n          if (node.fingerprint) {\n            vlessProxy.fingerprint = node.fingerprint;\n          }\n        }\n\n        // 添加REALITY配置\n        if (node.reality) {\n          vlessProxy.tls = true;\n          vlessProxy.reality = true;\n          if (node.sni) {\n            vlessProxy.servername = node.sni;\n          }\n          if (node.fingerprint) {\n            vlessProxy.fingerprint = node.fingerprint;\n          }\n          if (node.publicKey) {\n            vlessProxy['reality-opts'] = {\n              'public-key': node.publicKey,\n              'short-id': node.shortId || ''\n            };\n          }\n        }\n\n        // 添加网络配置\n        if (node.network === 'ws') {\n          vlessProxy.network = 'ws';\n          vlessProxy['ws-opts'] = {\n            path: node.path || '/',\n            headers: node.host ? { Host: node.host } : {}\n          };\n        } else if (node.network === 'grpc') {\n          vlessProxy.network = 'grpc';\n          vlessProxy['grpc-opts'] = {\n            'grpc-service-name': node.serviceName || 'GunService'\n          };\n        } else if (node.network === 'h2') {\n          vlessProxy.network = 'h2';\n          vlessProxy['h2-opts'] = {\n            host: [node.host || node.server],\n            path: node.path || '/'\n          };\n        } else {\n          vlessProxy.network = 'tcp';\n        }\n\n        return vlessProxy;\n\n      case 'ss':\n        return {\n          name: node.name,\n          type: 'ss',\n          server: node.server,\n          port: node.port,\n          cipher: node.method,\n          password: node.password,\n          udp: true\n        };\n\n      case 'trojan':\n        const trojanProxy = {\n          name: node.name,\n          type: 'trojan',\n          server: node.server,\n          port: node.port,\n          password: node.password,\n          udp: true\n        };\n\n        // 添加SNI配置\n        if (node.sni) {\n          trojanProxy.sni = node.sni;\n        }\n\n        // 添加网络配置\n        if (node.network === 'ws') {\n          trojanProxy.network = 'ws';\n          trojanProxy['ws-opts'] = {\n            path: node.path || '/',\n            headers: node.host ? { Host: node.host } : {}\n          };\n        } else if (node.network === 'grpc') {\n          trojanProxy.network = 'grpc';\n          trojanProxy['grpc-opts'] = {\n            'grpc-service-name': node.serviceName || 'GunService'\n          };\n        }\n\n        return trojanProxy;\n\n      case 'http':\n        const httpProxy = {\n          name: node.name,\n          type: 'http',\n          server: node.server,\n          port: node.port\n        };\n\n        if (node.username && node.password) {\n          httpProxy.username = node.username;\n          httpProxy.password = node.password;\n        }\n\n        if (node.tls) {\n          httpProxy.tls = true;\n        }\n\n        return httpProxy;\n\n      default:\n        return null;\n    }\n  }\n}\n", "import { Auth } from './auth.js';\nimport { NodeConverter } from './converter.js';\n\n// 模板文件内容 (在实际部署时需要读取文件)\nconst templates = {\n  css: `/* CSS content will be loaded here */`,\n  login: `/* Login HTML will be loaded here */`,\n  dashboard: `/* Dashboard HTML will be loaded here */`\n};\n\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n    const auth = new Auth(env);\n    const converter = new NodeConverter();\n\n    // 路由处理\n    switch (url.pathname) {\n      case '/':\n        return this.handleHome(request, auth, env);\n      \n      case '/login':\n        return this.handleLogin(request, auth, env);\n      \n      case '/logout':\n        return this.handleLogout(request, auth);\n      \n      case '/add-node':\n        return this.handleAddNode(request, auth, converter, env);\n      \n      case '/delete-node':\n        return this.handleDeleteNode(request, auth, env);\n      \n      case '/sub/v2ray':\n        return this.handleV2raySubscription(converter, env, request);\n\n      case '/sub/clash':\n        return this.handleClashSubscription(converter, env, request);\n\n      case '/sub/surge':\n        return this.handleSurgeSubscription(converter, env, request);\n      \n      default:\n        return new Response('Not Found', { status: 404 });\n    }\n  },\n\n  // 处理首页\n  async handleHome(request, auth, env) {\n    if (!auth.isAuthenticated(request)) {\n      return this.renderLogin();\n    }\n    return this.renderDashboard(request, env);\n  },\n\n  // 处理登录\n  async handleLogin(request, auth, env) {\n    if (request.method === 'GET') {\n      return this.renderLogin();\n    }\n\n    if (request.method === 'POST') {\n      const formData = await request.formData();\n      const username = formData.get('username');\n      const password = formData.get('password');\n\n      if (auth.validateCredentials(username, password)) {\n        const token = auth.generateToken(username);\n        const response = new Response('', {\n          status: 302,\n          headers: {\n            'Location': '/',\n            'Set-Cookie': auth.createAuthCookie(token)\n          }\n        });\n        return response;\n      } else {\n        return this.renderLogin('用户名或密码错误');\n      }\n    }\n  },\n\n  // 处理登出\n  async handleLogout(request, auth) {\n    return new Response('', {\n      status: 302,\n      headers: {\n        'Location': '/',\n        'Set-Cookie': auth.createLogoutCookie()\n      }\n    });\n  },\n\n  // 处理添加节点\n  async handleAddNode(request, auth, converter, env) {\n    if (!auth.isAuthenticated(request)) {\n      return new Response('Unauthorized', { status: 401 });\n    }\n\n    if (request.method === 'POST') {\n      try {\n        const formData = await request.formData();\n        const type = formData.get('type');\n        const name = formData.get('name');\n        const config = formData.get('config');\n\n        // 验证输入\n        if (!type || !name || !config) {\n          throw new Error('请填写完整的节点信息');\n        }\n\n        // 解析节点\n        const node = converter.parseNode(config.trim(), type);\n        node.name = name.trim(); // 使用用户提供的名称\n        node.id = Date.now().toString(); // 简单的ID生成\n\n        // 验证解析结果\n        if (!node.server || !node.port || !node.uuid) {\n          throw new Error('节点配置不完整，请检查链接格式');\n        }\n\n        // 获取现有节点\n        const existingNodes = await this.getNodes(env);\n\n        // 检查是否已存在相同的节点\n        const duplicate = existingNodes.find(existing =>\n          existing.server === node.server &&\n          existing.port === node.port &&\n          existing.uuid === node.uuid\n        );\n\n        if (duplicate) {\n          throw new Error('该节点已存在');\n        }\n\n        existingNodes.push(node);\n\n        // 保存到KV\n        await env.NODES_KV.put('nodes', JSON.stringify(existingNodes));\n\n        return new Response('', {\n          status: 302,\n          headers: { 'Location': '/' }\n        });\n      } catch (e) {\n        console.error('添加节点失败:', e);\n        return this.renderDashboard(request, env, `添加节点失败: ${e.message}`);\n      }\n    }\n  },\n\n  // 处理删除节点\n  async handleDeleteNode(request, auth, env) {\n    if (!auth.isAuthenticated(request)) {\n      return new Response('Unauthorized', { status: 401 });\n    }\n\n    if (request.method === 'POST') {\n      const { id } = await request.json();\n      const nodes = await this.getNodes(env);\n      const filteredNodes = nodes.filter(node => node.id !== id);\n      await env.NODES_KV.put('nodes', JSON.stringify(filteredNodes));\n      return new Response('OK');\n    }\n  },\n\n  // 处理V2Ray订阅\n  async handleV2raySubscription(converter, env, request) {\n    const nodes = await this.getNodes(env);\n    const subscription = converter.toV2raySubscription(nodes);\n\n    // 检查是否是浏览器访问（通过User-Agent或Accept头判断）\n    const userAgent = request.headers.get('User-Agent') || '';\n    const accept = request.headers.get('Accept') || '';\n    const isBrowser = userAgent.includes('Mozilla') && accept.includes('text/html');\n\n    if (isBrowser) {\n      // 浏览器访问时显示配置内容\n      const decodedContent = atob(subscription); // 解码Base64\n      const html = this.generateConfigDisplayPage('V2Ray', decodedContent, nodes, subscription);\n      return new Response(html, {\n        headers: { 'Content-Type': 'text/html; charset=utf-8' }\n      });\n    } else {\n      // 客户端访问时返回订阅内容\n      return new Response(subscription, {\n        headers: {\n          'Content-Type': 'text/plain; charset=utf-8',\n          'Cache-Control': 'no-cache'\n        }\n      });\n    }\n  },\n\n  // 处理Clash订阅\n  async handleClashSubscription(converter, env, request) {\n    const nodes = await this.getNodes(env);\n    const clashConfig = converter.toClashConfig(nodes);\n\n    // 将JSON转换为YAML格式\n    const yamlContent = this.jsonToYaml(clashConfig);\n\n    // 检查是否是浏览器访问\n    const userAgent = request.headers.get('User-Agent') || '';\n    const accept = request.headers.get('Accept') || '';\n    const isBrowser = userAgent.includes('Mozilla') && accept.includes('text/html');\n\n    if (isBrowser) {\n      // 浏览器访问时显示在网页上\n      const html = this.generateSubscriptionPage('Clash', yamlContent, nodes);\n      return new Response(html, {\n        headers: { 'Content-Type': 'text/html; charset=utf-8' }\n      });\n    } else {\n      // 客户端访问时返回YAML内容\n      return new Response(yamlContent, {\n        headers: {\n          'Content-Type': 'text/yaml; charset=utf-8',\n          'Cache-Control': 'no-cache'\n        }\n      });\n    }\n  },\n\n  // 处理Surge订阅\n  async handleSurgeSubscription(converter, env, request) {\n    const nodes = await this.getNodes(env);\n    const surgeConfig = this.generateSurgeConfig(nodes);\n\n    // 检查是否是浏览器访问\n    const userAgent = request.headers.get('User-Agent') || '';\n    const accept = request.headers.get('Accept') || '';\n    const isBrowser = userAgent.includes('Mozilla') && accept.includes('text/html');\n\n    if (isBrowser) {\n      // 浏览器访问时显示在网页上\n      const html = this.generateSubscriptionPage('Surge', surgeConfig, nodes);\n      return new Response(html, {\n        headers: { 'Content-Type': 'text/html; charset=utf-8' }\n      });\n    } else {\n      // 客户端访问时返回配置内容\n      return new Response(surgeConfig, {\n        headers: {\n          'Content-Type': 'text/plain; charset=utf-8',\n          'Cache-Control': 'no-cache'\n        }\n      });\n    }\n  },\n\n  // 获取节点列表\n  async getNodes(env) {\n    try {\n      const nodesData = await env.NODES_KV.get('nodes');\n      return nodesData ? JSON.parse(nodesData) : [];\n    } catch (e) {\n      return [];\n    }\n  },\n\n  // 渲染登录页面\n  renderLogin(errorMessage = '') {\n    const css = this.getCSS();\n    let html = this.getLoginHTML();\n    html = html.replace('{{CSS}}', css);\n    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ? \n      `<div class=\"alert alert-error\">${errorMessage}</div>` : '');\n    \n    return new Response(html, {\n      headers: { 'Content-Type': 'text/html; charset=utf-8' }\n    });\n  },\n\n  // 渲染管理面板\n  async renderDashboard(request, env, errorMessage = '') {\n    const css = this.getCSS();\n    const nodes = await this.getNodes(env);\n    const baseUrl = new URL(request.url).origin;\n\n    let html = this.getDashboardHTML();\n    html = html.replace('{{CSS}}', css);\n    // 替换所有的 {{BASE_URL}} 占位符\n    html = html.replace(/\\{\\{BASE_URL\\}\\}/g, baseUrl);\n    html = html.replace('{{NODES_LIST}}', this.generateNodesList(nodes));\n    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ?\n      `<div class=\"alert alert-error\">${errorMessage}</div>` : '');\n\n    return new Response(html, {\n      headers: { 'Content-Type': 'text/html; charset=utf-8' }\n    });\n  },\n\n  // 生成节点列表HTML\n  generateNodesList(nodes) {\n    if (nodes.length === 0) {\n      return '<p>暂无节点</p>';\n    }\n    \n    return nodes.map(node => `\n      <div class=\"node-item\">\n        <div class=\"node-header\">\n          <span class=\"node-type\">${node.type.toUpperCase()}</span>\n          <button class=\"btn-danger\" onclick=\"deleteNode('${node.id}')\">删除</button>\n        </div>\n        <div class=\"node-details\">\n          <strong>${node.name}</strong><br>\n          ${node.server}:${node.port}\n        </div>\n      </div>\n    `).join('');\n  },\n\n  // 生成Surge配置\n  generateSurgeConfig(nodes) {\n    const proxies = nodes.map(node => {\n      switch (node.type) {\n        case 'ss':\n          return `${node.name} = ss, ${node.server}, ${node.port}, encrypt-method=${node.method}, password=${node.password}`;\n        case 'http':\n          return `${node.name} = http, ${node.server}, ${node.port}${node.username ? `, username=${node.username}, password=${node.password}` : ''}`;\n        default:\n          return null;\n      }\n    }).filter(Boolean);\n\n    return `[General]\nskip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, localhost, *.local\n\n[Proxy]\n${proxies.join('\\n')}\n\n[Proxy Group]\nProxy = select, ${nodes.map(n => n.name).join(', ')}\n\n[Rule]\nFINAL,Proxy`;\n  },\n\n  // 简单的JSON到YAML转换器\n  jsonToYaml(obj, indent = 0) {\n    const spaces = '  '.repeat(indent);\n    let yaml = '';\n\n    if (Array.isArray(obj)) {\n      for (const item of obj) {\n        if (typeof item === 'object' && item !== null) {\n          yaml += `${spaces}- ${this.jsonToYaml(item, indent + 1).trim()}\\n`;\n        } else {\n          yaml += `${spaces}- ${this.escapeYamlValue(item)}\\n`;\n        }\n      }\n    } else if (typeof obj === 'object' && obj !== null) {\n      for (const [key, value] of Object.entries(obj)) {\n        if (Array.isArray(value)) {\n          yaml += `${spaces}${key}:\\n`;\n          yaml += this.jsonToYaml(value, indent + 1);\n        } else if (typeof value === 'object' && value !== null) {\n          yaml += `${spaces}${key}:\\n`;\n          yaml += this.jsonToYaml(value, indent + 1);\n        } else {\n          yaml += `${spaces}${key}: ${this.escapeYamlValue(value)}\\n`;\n        }\n      }\n    }\n\n    return yaml;\n  },\n\n  // 转义YAML值\n  escapeYamlValue(value) {\n    if (typeof value === 'string') {\n      // 如果字符串包含特殊字符，需要加引号\n      if (value.includes(':') || value.includes('#') || value.includes('[') ||\n          value.includes(']') || value.includes('{') || value.includes('}') ||\n          value.includes('|') || value.includes('>') || value.includes('&') ||\n          value.includes('*') || value.includes('!') || value.includes('%') ||\n          value.includes('@') || value.includes('`')) {\n        return `\"${value.replace(/\"/g, '\\\\\"')}\"`;\n      }\n      return value;\n    }\n    return value;\n  },\n\n  // 生成订阅页面\n  generateSubscriptionPage(type, content, nodes) {\n    const nodeCount = nodes.length;\n    const contentPreview = content.length > 1000 ? content.substring(0, 1000) + '...' : content;\n\n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${type} 订阅 - 订阅转换器</title>\n    <style>\n        ${this.getCSS()}\n        .subscription-container {\n            max-width: 1000px;\n            margin: 2rem auto;\n            padding: 1rem;\n        }\n        .subscription-header {\n            text-align: center;\n            margin-bottom: 2rem;\n        }\n        .subscription-info {\n            background: #f8f9fa;\n            border-radius: 10px;\n            padding: 1.5rem;\n            margin-bottom: 2rem;\n        }\n        .subscription-content {\n            background: #2d3748;\n            color: #e2e8f0;\n            border-radius: 10px;\n            padding: 1.5rem;\n            font-family: 'Courier New', monospace;\n            font-size: 0.9rem;\n            line-height: 1.4;\n            overflow-x: auto;\n            white-space: pre-wrap;\n            word-break: break-all;\n        }\n        .copy-all-btn {\n            background: #28a745;\n            color: white;\n            border: none;\n            padding: 0.75rem 1.5rem;\n            border-radius: 5px;\n            cursor: pointer;\n            font-size: 1rem;\n            margin: 1rem 0;\n        }\n        .copy-all-btn:hover {\n            background: #218838;\n        }\n        .stats {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 1rem;\n            margin-bottom: 2rem;\n        }\n        .stat-item {\n            background: white;\n            border-radius: 8px;\n            padding: 1rem;\n            text-align: center;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n        .stat-number {\n            font-size: 2rem;\n            font-weight: bold;\n            color: #667eea;\n        }\n        .stat-label {\n            color: #666;\n            margin-top: 0.5rem;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"subscription-container\">\n        <div class=\"subscription-header\">\n            <h1>📋 ${type} 订阅</h1>\n            <p style=\"color: #666;\">订阅转换器生成的配置文件</p>\n        </div>\n\n        <div class=\"stats\">\n            <div class=\"stat-item\">\n                <div class=\"stat-number\">${nodeCount}</div>\n                <div class=\"stat-label\">节点数量</div>\n            </div>\n            <div class=\"stat-item\">\n                <div class=\"stat-number\">${Math.round(content.length / 1024)}KB</div>\n                <div class=\"stat-label\">配置大小</div>\n            </div>\n            <div class=\"stat-item\">\n                <div class=\"stat-number\">${type}</div>\n                <div class=\"stat-label\">订阅类型</div>\n            </div>\n        </div>\n\n        <div class=\"subscription-info\">\n            <h3>📖 使用说明</h3>\n            <ul>\n                <li><strong>客户端导入</strong>: 复制当前页面URL到${type}客户端的订阅链接中</li>\n                <li><strong>手动配置</strong>: 点击下方\"复制全部内容\"按钮，手动粘贴到客户端</li>\n                <li><strong>自动更新</strong>: 客户端会定期从此链接更新配置</li>\n                <li><strong>浏览器访问</strong>: 显示此页面；客户端访问: 直接返回配置内容</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center;\">\n            <button class=\"copy-all-btn\" onclick=\"copyAllContent()\">📋 复制全部内容</button>\n        </div>\n\n        <div class=\"subscription-content\" id=\"content\">${contentPreview}</div>\n\n        <div style=\"text-align: center; margin-top: 2rem;\">\n            <a href=\"/\" style=\"color: #667eea; text-decoration: none;\">← 返回管理面板</a>\n        </div>\n    </div>\n\n    <script>\n        function copyAllContent() {\n            const fullContent = \\`${content.replace(/`/g, '\\\\`').replace(/\\$/g, '\\\\$')}\\`;\n            navigator.clipboard.writeText(fullContent).then(function() {\n                const btn = document.querySelector('.copy-all-btn');\n                const originalText = btn.textContent;\n                btn.textContent = '✅ 已复制!';\n                btn.style.background = '#28a745';\n                setTimeout(() => {\n                    btn.textContent = originalText;\n                    btn.style.background = '#28a745';\n                }, 2000);\n            }).catch(function(err) {\n                console.error('复制失败:', err);\n                alert('复制失败，请手动选择内容复制');\n            });\n        }\n    </script>\n</body>\n</html>`;\n  },\n\n  // 获取CSS内容\n  getCSS() {\n    return `* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  margin: 0;\n  padding: 0;\n}\n\n/* 登录页面样式 */\n.login-body {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  padding: 1rem;\n}\n\n.container {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  width: 100%;\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n/* 管理面板样式 */\n.dashboard-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 1rem;\n  min-height: 100vh;\n}\n\n/* 响应式网格布局 */\n.dashboard-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n  margin-top: 2rem;\n}\n\n@media (min-width: 768px) {\n  .dashboard-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n@media (min-width: 1024px) {\n  .dashboard-grid {\n    grid-template-columns: 1fr 1fr 1fr;\n  }\n}\n\nh1, h2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #555;\n  font-weight: 500;\n}\n\ninput, textarea, select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e1e5e9;\n  border-radius: 5px;\n  font-size: 1rem;\n  transition: border-color 0.3s;\n}\n\ninput:focus, textarea:focus, select:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\nbutton {\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\nbutton:hover {\n  transform: translateY(-2px);\n}\n\n.btn-secondary {\n  background: #6c757d;\n  margin-top: 0.5rem;\n}\n\n.btn-danger {\n  background: #dc3545;\n  width: auto;\n  padding: 0.5rem 1rem;\n  margin-left: 0.5rem;\n}\n\n.node-item {\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 5px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n\n.node-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.node-type {\n  background: #007bff;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.8rem;\n}\n\n.node-details {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n.subscription-links {\n  background: #e9ecef;\n  border-radius: 5px;\n  padding: 1rem;\n  margin-top: 2rem;\n}\n\n.link-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n  padding: 0.75rem;\n  background: white;\n  border-radius: 5px;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.link-item span {\n  font-weight: 500;\n  color: #333;\n  min-width: 100px;\n}\n\n.copy-btn {\n  background: #28a745;\n  width: auto;\n  padding: 0.5rem 1rem;\n  font-size: 0.9rem;\n  min-width: 80px;\n  flex-shrink: 0;\n}\n\n/* 移动端优化 */\n@media (max-width: 768px) {\n  .container {\n    padding: 1.5rem;\n    margin: 0.5rem;\n    max-width: none;\n  }\n\n  .dashboard-container {\n    padding: 0.5rem;\n  }\n\n  .link-item {\n    flex-direction: column;\n    align-items: stretch;\n    text-align: center;\n  }\n\n  .link-item span {\n    margin-bottom: 0.5rem;\n    min-width: auto;\n  }\n\n  .copy-btn {\n    width: 100%;\n    min-width: auto;\n  }\n\n  .node-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 0.5rem;\n  }\n\n  .btn-danger {\n    margin-left: 0;\n    width: 100%;\n  }\n}\n\n.alert {\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  border-radius: 5px;\n}\n\n.alert-success {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.alert-error {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}`;\n  },\n\n  // 获取登录页面HTML\n  getLoginHTML() {\n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>订阅转换器 - 登录</title>\n    <style>{{CSS}}</style>\n</head>\n<body class=\"login-body\">\n    <div class=\"container\">\n        <h1>🚀 订阅转换器</h1>\n        <p style=\"text-align: center; color: #666; margin-bottom: 2rem;\">安全的节点订阅管理平台</p>\n        {{ERROR_MESSAGE}}\n        <form method=\"POST\" action=\"/login\">\n            <div class=\"form-group\">\n                <label for=\"username\">👤 用户名:</label>\n                <input type=\"text\" id=\"username\" name=\"username\" required autocomplete=\"username\">\n            </div>\n            <div class=\"form-group\">\n                <label for=\"password\">🔒 密码:</label>\n                <input type=\"password\" id=\"password\" name=\"password\" required autocomplete=\"current-password\">\n            </div>\n            <button type=\"submit\">登录</button>\n        </form>\n    </div>\n</body>\n</html>`;\n  },\n\n  // 获取管理面板HTML\n  getDashboardHTML() {\n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>订阅转换器 - 管理面板</title>\n    <style>{{CSS}}</style>\n</head>\n<body>\n    <div class=\"dashboard-container\">\n        <header style=\"text-align: center; margin-bottom: 2rem;\">\n            <h1>🚀 节点管理面板</h1>\n            <p style=\"color: #666;\">管理你的代理节点和订阅链接</p>\n        </header>\n\n        {{ERROR_MESSAGE}}\n\n        <div class=\"dashboard-grid\">\n            <!-- 添加节点表单 -->\n            <div class=\"container\">\n                <h2>➕ 添加新节点</h2>\n                <form method=\"POST\" action=\"/add-node\">\n                    <div class=\"form-group\">\n                        <label for=\"node-type\">🔧 节点类型:</label>\n                        <select id=\"node-type\" name=\"type\" required>\n                            <option value=\"\">选择节点类型</option>\n                            <option value=\"vmess\">VMess</option>\n                            <option value=\"vless\">VLess</option>\n                            <option value=\"ss\">Shadowsocks</option>\n                            <option value=\"trojan\">Trojan</option>\n                            <option value=\"http\">HTTP/HTTPS</option>\n                        </select>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"node-name\">📝 节点名称:</label>\n                        <input type=\"text\" id=\"node-name\" name=\"name\" required placeholder=\"例如: 香港节点01\">\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"node-config\">⚙️ 节点配置:</label>\n                        <textarea id=\"node-config\" name=\"config\" rows=\"4\" required placeholder=\"粘贴节点链接或配置信息\"></textarea>\n                    </div>\n                    <button type=\"submit\">添加节点</button>\n                </form>\n            </div>\n\n            <!-- 订阅链接 -->\n            <div class=\"container subscription-links\">\n                <h2>📋 订阅链接</h2>\n                <div class=\"link-item\">\n                    <span>📱 V2Ray订阅:</span>\n                    <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/v2ray')\">复制链接</button>\n                </div>\n                <div class=\"link-item\">\n                    <span>⚔️ Clash订阅:</span>\n                    <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/clash')\">复制链接</button>\n                </div>\n                <div class=\"link-item\">\n                    <span>🌊 Surge订阅:</span>\n                    <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/surge')\">复制链接</button>\n                </div>\n                <div style=\"margin-top: 1rem; padding: 0.75rem; background: #e3f2fd; border-radius: 5px; font-size: 0.9rem; color: #1565c0;\">\n                    💡 提示: 复制订阅链接到你的代理客户端中使用\n                </div>\n            </div>\n        </div>\n\n        <!-- 节点列表 -->\n        <div class=\"container\" style=\"margin-top: 1.5rem;\">\n            <h2>📊 已添加的节点</h2>\n            {{NODES_LIST}}\n        </div>\n\n        <!-- 退出登录 -->\n        <div class=\"container\" style=\"margin-top: 1.5rem; text-align: center;\">\n            <form method=\"POST\" action=\"/logout\">\n                <button type=\"submit\" class=\"btn-secondary\">🚪 退出登录</button>\n            </form>\n        </div>\n    </div>\n\n    <script>\n        function copyToClipboard(text) {\n            navigator.clipboard.writeText(text).then(function() {\n                showNotification('✅ 链接已复制到剪贴板', 'success');\n            }).catch(function(err) {\n                console.error('复制失败:', err);\n                showNotification('❌ 复制失败，请手动复制', 'error');\n                // 降级方案：显示链接让用户手动复制\n                prompt('请手动复制以下链接:', text);\n            });\n        }\n\n        function deleteNode(nodeId) {\n            if (confirm('确定要删除这个节点吗？')) {\n                const button = event.target;\n                button.disabled = true;\n                button.textContent = '删除中...';\n\n                fetch('/delete-node', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    },\n                    body: JSON.stringify({ id: nodeId })\n                }).then(response => {\n                    if (response.ok) {\n                        showNotification('✅ 节点删除成功', 'success');\n                        setTimeout(() => location.reload(), 1000);\n                    } else {\n                        throw new Error('删除失败');\n                    }\n                }).catch(error => {\n                    console.error('删除失败:', error);\n                    showNotification('❌ 删除失败，请重试', 'error');\n                    button.disabled = false;\n                    button.textContent = '删除';\n                });\n            }\n        }\n\n        function showNotification(message, type) {\n            // 创建通知元素\n            const notification = document.createElement('div');\n            notification.className = \\`alert alert-\\${type === 'success' ? 'success' : 'error'}\\`;\n            notification.style.cssText = \\`\n                position: fixed;\n                top: 20px;\n                right: 20px;\n                z-index: 1000;\n                min-width: 300px;\n                animation: slideIn 0.3s ease-out;\n            \\`;\n            notification.textContent = message;\n\n            // 添加动画样式\n            if (!document.querySelector('#notification-styles')) {\n                const style = document.createElement('style');\n                style.id = 'notification-styles';\n                style.textContent = \\`\n                    @keyframes slideIn {\n                        from { transform: translateX(100%); opacity: 0; }\n                        to { transform: translateX(0); opacity: 1; }\n                    }\n                    @keyframes slideOut {\n                        from { transform: translateX(0); opacity: 1; }\n                        to { transform: translateX(100%); opacity: 0; }\n                    }\n                \\`;\n                document.head.appendChild(style);\n            }\n\n            document.body.appendChild(notification);\n\n            // 3秒后自动移除\n            setTimeout(() => {\n                notification.style.animation = 'slideOut 0.3s ease-in';\n                setTimeout(() => {\n                    if (notification.parentNode) {\n                        notification.parentNode.removeChild(notification);\n                    }\n                }, 300);\n            }, 3000);\n        }\n\n        // 表单提交增强\n        document.addEventListener('DOMContentLoaded', function() {\n            const form = document.querySelector('form[action=\"/add-node\"]');\n            if (form) {\n                form.addEventListener('submit', function(e) {\n                    const button = form.querySelector('button[type=\"submit\"]');\n                    button.disabled = true;\n                    button.textContent = '添加中...';\n\n                    // 如果表单提交失败，恢复按钮状态\n                    setTimeout(() => {\n                        button.disabled = false;\n                        button.textContent = '添加节点';\n                    }, 5000);\n                });\n            }\n        });\n    </script>\n</body>\n</html>`;\n  }\n};\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\Git\\\\sub_conventer\\\\src\\\\index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"E:\\\\Git\\\\sub_conventer\\\\src\\\\index.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-7hdGrO\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-7hdGrO\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-7hdGrO\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACVM,IAAM,OAAN,MAAW;AAAA,EAChB,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA;AAAA,EAGA,oBAAoB,UAAU,UAAU;AACtC,WAAO,aAAa,KAAK,IAAI,kBAAkB,aAAa,KAAK,IAAI;AAAA,EACvE;AAAA;AAAA,EAGA,cAAc,UAAU;AACtB,UAAM,UAAU;AAAA,MACd;AAAA,MACA,KAAK,KAAK,IAAI,IAAK,KAAK,KAAK,KAAK;AAAA;AAAA,IACpC;AACA,WAAO,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EACrC;AAAA;AAAA,EAGA,cAAc,OAAO;AACnB,QAAI;AACF,YAAM,UAAU,KAAK,MAAM,KAAK,KAAK,CAAC;AACtC,UAAI,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC5B,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,aAAa,KAAK,IAAI;AAAA,IACvC,SAAS,GAAP;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,oBAAoB,SAAS;AAC3B,UAAM,SAAS,QAAQ,QAAQ,IAAI,QAAQ;AAC3C,QAAI,CAAC;AAAQ,aAAO;AAEpB,UAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,WAAO,QAAQ,MAAM,CAAC,IAAI;AAAA,EAC5B;AAAA;AAAA,EAGA,gBAAgB,SAAS;AACvB,UAAM,QAAQ,KAAK,oBAAoB,OAAO;AAC9C,WAAO,SAAS,KAAK,cAAc,KAAK;AAAA,EAC1C;AAAA;AAAA,EAGA,iBAAiB,OAAO;AACtB,WAAO,cAAc;AAAA,EACvB;AAAA;AAAA,EAGA,qBAAqB;AACnB,WAAO;AAAA,EACT;AACF;AAxDa;;;ACAN,IAAM,gBAAN,MAAoB;AAAA,EACzB,cAAc;AAAA,EAAC;AAAA;AAAA,EAGf,UAAU,QAAQ,MAAM;AACtB,QAAI;AACF,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO,KAAK,WAAW,MAAM;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,WAAW,MAAM;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,iBAAiB,MAAM;AAAA,QACrC,KAAK;AACH,iBAAO,KAAK,YAAY,MAAM;AAAA,QAChC,KAAK;AACH,iBAAO,KAAK,UAAU,MAAM;AAAA,QAC9B;AACE,gBAAM,IAAI,MAAM,kDAAU;AAAA,MAC9B;AAAA,IACF,SAAS,GAAP;AACA,YAAM,IAAI,MAAM,yCAAW,EAAE,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAGA,WAAW,QAAQ;AACjB,QAAI,OAAO,WAAW,UAAU,GAAG;AACjC,YAAM,OAAO,KAAK,MAAM,KAAK,OAAO,UAAU,CAAC,CAAC,CAAC;AACjD,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,KAAK,MAAM;AAAA,QACjB,QAAQ,KAAK;AAAA,QACb,MAAM,SAAS,KAAK,IAAI;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,SAAS,SAAS,KAAK,GAAG,KAAK;AAAA,QAC/B,QAAQ,KAAK,OAAO;AAAA,QACpB,SAAS,KAAK,OAAO;AAAA,QACrB,KAAK,KAAK,QAAQ;AAAA,QAClB,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,UAAM,IAAI,MAAM,qCAAY;AAAA,EAC9B;AAAA;AAAA,EAGA,WAAW,QAAQ;AACjB,QAAI,OAAO,WAAW,UAAU,GAAG;AACjC,UAAI;AACF,cAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,cAAM,SAAS,IAAI;AAGnB,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,UACnD,QAAQ,IAAI;AAAA,UACZ,MAAM,SAAS,IAAI,IAAI;AAAA,UACvB,MAAM,IAAI;AAAA,UACV,YAAY,OAAO,IAAI,YAAY,KAAK;AAAA,UACxC,SAAS,OAAO,IAAI,MAAM,KAAK;AAAA,QACjC;AAGA,YAAI,CAAC,KAAK,UAAU,CAAC,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC5C,gBAAM,IAAI,MAAM,uDAAe;AAAA,QACjC;AAGA,cAAM,WAAW,OAAO,IAAI,UAAU;AACtC,YAAI,aAAa,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;AACpC,eAAK,OAAO,OAAO,IAAI,MAAM;AAC7B,eAAK,cAAc,OAAO,IAAI,IAAI;AAAA,QACpC,WAAW,aAAa,WAAW;AACjC,eAAK,UAAU;AACf,eAAK,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;AACpC,eAAK,cAAc,OAAO,IAAI,IAAI,KAAK;AACvC,eAAK,YAAY,OAAO,IAAI,KAAK;AACjC,eAAK,UAAU,OAAO,IAAI,KAAK;AAC/B,eAAK,UAAU,OAAO,IAAI,KAAK;AAAA,QACjC;AAGA,cAAM,OAAO,OAAO,IAAI,MAAM;AAC9B,YAAI,MAAM;AACR,eAAK,OAAO;AAAA,QACd;AAGA,gBAAQ,KAAK,SAAS;AAAA,UACpB,KAAK;AACH,iBAAK,aAAa,OAAO,IAAI,YAAY,KAAK;AAC9C;AAAA,UACF,KAAK;AACH,iBAAK,OAAO,OAAO,IAAI,MAAM,KAAK;AAClC,iBAAK,OAAO,OAAO,IAAI,MAAM,KAAK;AAClC;AAAA,UACF,KAAK;AACH,iBAAK,cAAc,OAAO,IAAI,aAAa,KAAK;AAChD,iBAAK,OAAO,OAAO,IAAI,MAAM,KAAK;AAClC;AAAA,UACF,KAAK;AACH,iBAAK,OAAO,OAAO,IAAI,MAAM,KAAK;AAClC,iBAAK,OAAO,OAAO,IAAI,MAAM,KAAK;AAClC;AAAA,UACF,KAAK;AACH,iBAAK,aAAa,OAAO,IAAI,YAAY,KAAK;AAC9C,iBAAK,OAAO,OAAO,IAAI,MAAM;AAC7B;AAAA,UACF,KAAK;AACH,iBAAK,aAAa,OAAO,IAAI,YAAY,KAAK;AAC9C,iBAAK,eAAe,OAAO,IAAI,cAAc,KAAK;AAClD,iBAAK,MAAM,OAAO,IAAI,KAAK;AAC3B;AAAA,QACJ;AAEA,eAAO;AAAA,MACT,SAAS,OAAP;AACA,cAAM,IAAI,MAAM,8CAAgB,MAAM,SAAS;AAAA,MACjD;AAAA,IACF;AACA,UAAM,IAAI,MAAM,qCAAY;AAAA,EAC9B;AAAA;AAAA,EAGA,iBAAiB,QAAQ;AACvB,QAAI,OAAO,WAAW,OAAO,GAAG;AAC9B,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,YAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,YAAM,CAAC,QAAQ,QAAQ,IAAI,SAAS,MAAM,GAAG;AAE7C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,QACnD,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,MAAM,2CAAkB;AAAA,EACpC;AAAA;AAAA,EAGA,YAAY,QAAQ;AAClB,QAAI,OAAO,WAAW,WAAW,GAAG;AAClC,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,QACnD,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI;AAAA,QACvB,UAAU,IAAI;AAAA,QACd,KAAK,IAAI,aAAa,IAAI,KAAK,KAAK,IAAI;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,IAAI,MAAM,sCAAa;AAAA,EAC/B;AAAA;AAAA,EAGA,UAAU,QAAQ;AAChB,QAAI,OAAO,WAAW,SAAS,KAAK,OAAO,WAAW,UAAU,GAAG;AACjE,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,QAAQ,IAAI;AAAA,QAClB,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI,aAAa,WAAW,MAAM;AAAA,QAC/D,UAAU,IAAI,YAAY;AAAA,QAC1B,UAAU,IAAI,YAAY;AAAA,QAC1B,KAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF;AACA,UAAM,IAAI,MAAM,oCAAW;AAAA,EAC7B;AAAA;AAAA,EAGA,oBAAoB,OAAO;AACzB,UAAM,UAAU,MAAM,IAAI,UAAQ;AAChC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC,KAAK;AACH,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC,KAAK;AACH,iBAAO,KAAK,aAAa,IAAI;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,iBAAiB,IAAI;AAAA,QACnC;AACE,iBAAO;AAAA,MACX;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AAEjB,WAAO,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAChC;AAAA;AAAA,EAGA,cAAc,OAAO;AACnB,UAAM,UAAU,MAAM,IAAI,UAAQ,KAAK,iBAAiB,IAAI,CAAC,EAAE,OAAO,OAAO;AAC7E,UAAM,aAAa,QAAQ,IAAI,OAAK,EAAE,IAAI;AAG1C,QAAI,WAAW,WAAW,GAAG;AAC3B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,cAAc;AAAA,QACd,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,uBAAuB;AAAA,QACvB,SAAS,CAAC;AAAA,QACV,gBAAgB;AAAA,UACd;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS,CAAC,QAAQ;AAAA,UACpB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC,yCAAW,oCAAS,EAAE,OAAO,UAAU;AAAA,MACnD;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAAA,MACpB;AAAA,IACF;AAGA,QAAI,WAAW,SAAS,GAAG;AACzB,kBAAY,OAAO,GAAG,GAAG;AAAA,QACvB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,MACb,uBAAuB;AAAA,MACvB;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,gBAAgB,MAAM;AACpB,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,IAAI,KAAK;AAAA,MACT,KAAK,KAAK;AAAA,MACV,MAAM,KAAK,KAAK,SAAS;AAAA,MACzB,IAAI,KAAK;AAAA,MACT,KAAK,KAAK,QAAQ,SAAS;AAAA,MAC3B,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,KAAK,KAAK,MAAM,QAAQ;AAAA,IAC1B;AACA,WAAO,aAAa,KAAK,KAAK,UAAU,QAAQ,CAAC;AAAA,EACnD;AAAA;AAAA,EAGA,gBAAgB,MAAM;AACpB,UAAM,SAAS,IAAI,gBAAgB;AACnC,WAAO,IAAI,cAAc,KAAK,cAAc,MAAM;AAClD,WAAO,IAAI,QAAQ,KAAK,WAAW,KAAK;AAGxC,QAAI,KAAK,SAAS;AAChB,aAAO,IAAI,YAAY,SAAS;AAChC,UAAI,KAAK;AAAK,eAAO,IAAI,OAAO,KAAK,GAAG;AACxC,UAAI,KAAK;AAAa,eAAO,IAAI,MAAM,KAAK,WAAW;AACvD,UAAI,KAAK;AAAW,eAAO,IAAI,OAAO,KAAK,SAAS;AACpD,UAAI,KAAK;AAAS,eAAO,IAAI,OAAO,KAAK,OAAO;AAChD,UAAI,KAAK;AAAS,eAAO,IAAI,OAAO,KAAK,OAAO;AAAA,IAClD,WAAW,KAAK,KAAK;AACnB,aAAO,IAAI,YAAY,KAAK;AAC5B,UAAI,KAAK;AAAK,eAAO,IAAI,OAAO,KAAK,GAAG;AACxC,UAAI,KAAK;AAAM,eAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C,UAAI,KAAK;AAAa,eAAO,IAAI,MAAM,KAAK,WAAW;AAAA,IACzD;AAGA,QAAI,KAAK;AAAM,aAAO,IAAI,QAAQ,KAAK,IAAI;AAG3C,YAAQ,KAAK,SAAS;AAAA,MACpB,KAAK;AACH,YAAI,KAAK;AAAM,iBAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C,YAAI,KAAK;AAAM,iBAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C;AAAA,MACF,KAAK;AACH,YAAI,KAAK;AAAa,iBAAO,IAAI,eAAe,KAAK,WAAW;AAChE,YAAI,KAAK;AAAM,iBAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C;AAAA,MACF,KAAK;AACH,YAAI,KAAK;AAAM,iBAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C,YAAI,KAAK;AAAM,iBAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C;AAAA,MACF,KAAK;AACH,YAAI,KAAK,cAAc,KAAK,eAAe,QAAQ;AACjD,iBAAO,IAAI,cAAc,KAAK,UAAU;AAAA,QAC1C;AACA;AAAA,IACJ;AAEA,WAAO,WAAW,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,OAAO,SAAS,KAAK,mBAAmB,KAAK,IAAI;AAAA,EAC9G;AAAA;AAAA,EAGA,aAAa,MAAM;AACjB,UAAM,WAAW,KAAK,GAAG,KAAK,UAAU,KAAK,UAAU;AACvD,WAAO,QAAQ,YAAY,KAAK,UAAU,KAAK,QAAQ,mBAAmB,KAAK,IAAI;AAAA,EACrF;AAAA;AAAA,EAGA,iBAAiB,MAAM;AACrB,UAAM,SAAS,IAAI,gBAAgB;AACnC,QAAI,KAAK;AAAK,aAAO,IAAI,OAAO,KAAK,GAAG;AAExC,WAAO,YAAY,KAAK,YAAY,KAAK,UAAU,KAAK,QAAQ,OAAO,SAAS,KAAK,mBAAmB,KAAK,IAAI;AAAA,EACnH;AAAA;AAAA,EAGA,iBAAiB,MAAM;AACrB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,cAAM,aAAa;AAAA,UACjB,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX,SAAS,KAAK,WAAW;AAAA,UACzB,QAAQ,KAAK,UAAU;AAAA,QACzB;AAGA,YAAI,KAAK,KAAK;AACZ,qBAAW,MAAM;AAAA,QACnB;AAGA,YAAI,KAAK,YAAY,MAAM;AACzB,qBAAW,UAAU;AACrB,qBAAW,SAAS,IAAI;AAAA,YACtB,MAAM,KAAK,QAAQ;AAAA,YACnB,SAAS,KAAK,OAAO,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,UAC9C;AAAA,QACF,WAAW,KAAK,YAAY,MAAM;AAChC,qBAAW,UAAU;AACrB,qBAAW,SAAS,IAAI;AAAA,YACtB,MAAM,CAAC,KAAK,QAAQ,KAAK,MAAM;AAAA,YAC/B,MAAM,KAAK,QAAQ;AAAA,UACrB;AAAA,QACF,OAAO;AACL,qBAAW,UAAU;AAAA,QACvB;AAEA,eAAO;AAAA,MAET,KAAK;AACH,cAAM,aAAa;AAAA,UACjB,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX,mBAAmB;AAAA,QACrB;AAGA,YAAI,KAAK,MAAM;AACb,qBAAW,OAAO,KAAK;AAAA,QACzB;AAGA,YAAI,KAAK,KAAK;AACZ,qBAAW,MAAM;AACjB,cAAI,KAAK,KAAK;AACZ,uBAAW,aAAa,KAAK;AAAA,UAC/B;AACA,cAAI,KAAK,MAAM;AACb,uBAAW,OAAO,KAAK,KAAK,MAAM,GAAG;AAAA,UACvC;AACA,cAAI,KAAK,aAAa;AACpB,uBAAW,cAAc,KAAK;AAAA,UAChC;AAAA,QACF;AAGA,YAAI,KAAK,SAAS;AAChB,qBAAW,MAAM;AACjB,qBAAW,UAAU;AACrB,cAAI,KAAK,KAAK;AACZ,uBAAW,aAAa,KAAK;AAAA,UAC/B;AACA,cAAI,KAAK,aAAa;AACpB,uBAAW,cAAc,KAAK;AAAA,UAChC;AACA,cAAI,KAAK,WAAW;AAClB,uBAAW,cAAc,IAAI;AAAA,cAC3B,cAAc,KAAK;AAAA,cACnB,YAAY,KAAK,WAAW;AAAA,YAC9B;AAAA,UACF;AAAA,QACF;AAGA,YAAI,KAAK,YAAY,MAAM;AACzB,qBAAW,UAAU;AACrB,qBAAW,SAAS,IAAI;AAAA,YACtB,MAAM,KAAK,QAAQ;AAAA,YACnB,SAAS,KAAK,OAAO,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,UAC9C;AAAA,QACF,WAAW,KAAK,YAAY,QAAQ;AAClC,qBAAW,UAAU;AACrB,qBAAW,WAAW,IAAI;AAAA,YACxB,qBAAqB,KAAK,eAAe;AAAA,UAC3C;AAAA,QACF,WAAW,KAAK,YAAY,MAAM;AAChC,qBAAW,UAAU;AACrB,qBAAW,SAAS,IAAI;AAAA,YACtB,MAAM,CAAC,KAAK,QAAQ,KAAK,MAAM;AAAA,YAC/B,MAAM,KAAK,QAAQ;AAAA,UACrB;AAAA,QACF,OAAO;AACL,qBAAW,UAAU;AAAA,QACvB;AAEA,eAAO;AAAA,MAET,KAAK;AACH,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,UAAU,KAAK;AAAA,UACf,KAAK;AAAA,QACP;AAAA,MAEF,KAAK;AACH,cAAM,cAAc;AAAA,UAClB,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,KAAK;AAAA,QACP;AAGA,YAAI,KAAK,KAAK;AACZ,sBAAY,MAAM,KAAK;AAAA,QACzB;AAGA,YAAI,KAAK,YAAY,MAAM;AACzB,sBAAY,UAAU;AACtB,sBAAY,SAAS,IAAI;AAAA,YACvB,MAAM,KAAK,QAAQ;AAAA,YACnB,SAAS,KAAK,OAAO,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,UAC9C;AAAA,QACF,WAAW,KAAK,YAAY,QAAQ;AAClC,sBAAY,UAAU;AACtB,sBAAY,WAAW,IAAI;AAAA,YACzB,qBAAqB,KAAK,eAAe;AAAA,UAC3C;AAAA,QACF;AAEA,eAAO;AAAA,MAET,KAAK;AACH,cAAM,YAAY;AAAA,UAChB,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,QACb;AAEA,YAAI,KAAK,YAAY,KAAK,UAAU;AAClC,oBAAU,WAAW,KAAK;AAC1B,oBAAU,WAAW,KAAK;AAAA,QAC5B;AAEA,YAAI,KAAK,KAAK;AACZ,oBAAU,MAAM;AAAA,QAClB;AAEA,eAAO;AAAA,MAET;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACF;AAvhBa;;;ACQb,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,IAAI,KAAK,GAAG;AACzB,UAAM,YAAY,IAAI,cAAc;AAGpC,YAAQ,IAAI,UAAU;AAAA,MACpB,KAAK;AACH,eAAO,KAAK,WAAW,SAAS,MAAM,GAAG;AAAA,MAE3C,KAAK;AACH,eAAO,KAAK,YAAY,SAAS,MAAM,GAAG;AAAA,MAE5C,KAAK;AACH,eAAO,KAAK,aAAa,SAAS,IAAI;AAAA,MAExC,KAAK;AACH,eAAO,KAAK,cAAc,SAAS,MAAM,WAAW,GAAG;AAAA,MAEzD,KAAK;AACH,eAAO,KAAK,iBAAiB,SAAS,MAAM,GAAG;AAAA,MAEjD,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,KAAK,OAAO;AAAA,MAE7D,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,KAAK,OAAO;AAAA,MAE7D,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,KAAK,OAAO;AAAA,MAE7D;AACE,eAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,WAAW,SAAS,MAAM,KAAK;AACnC,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,WAAO,KAAK,gBAAgB,SAAS,GAAG;AAAA,EAC1C;AAAA;AAAA,EAGA,MAAM,YAAY,SAAS,MAAM,KAAK;AACpC,QAAI,QAAQ,WAAW,OAAO;AAC5B,aAAO,KAAK,YAAY;AAAA,IAC1B;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,YAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,YAAM,WAAW,SAAS,IAAI,UAAU;AACxC,YAAM,WAAW,SAAS,IAAI,UAAU;AAExC,UAAI,KAAK,oBAAoB,UAAU,QAAQ,GAAG;AAChD,cAAM,QAAQ,KAAK,cAAc,QAAQ;AACzC,cAAM,WAAW,IAAI,SAAS,IAAI;AAAA,UAChC,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,YAAY;AAAA,YACZ,cAAc,KAAK,iBAAiB,KAAK;AAAA,UAC3C;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,OAAO;AACL,eAAO,KAAK,YAAY,kDAAU;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,aAAa,SAAS,MAAM;AAChC,WAAO,IAAI,SAAS,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,cAAc,KAAK,mBAAmB;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,cAAc,SAAS,MAAM,WAAW,KAAK;AACjD,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,IAAI,SAAS,gBAAgB,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrD;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,UAAI;AACF,cAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,cAAM,OAAO,SAAS,IAAI,MAAM;AAChC,cAAM,OAAO,SAAS,IAAI,MAAM;AAChC,cAAM,SAAS,SAAS,IAAI,QAAQ;AAGpC,YAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ;AAC7B,gBAAM,IAAI,MAAM,8DAAY;AAAA,QAC9B;AAGA,cAAM,OAAO,UAAU,UAAU,OAAO,KAAK,GAAG,IAAI;AACpD,aAAK,OAAO,KAAK,KAAK;AACtB,aAAK,KAAK,KAAK,IAAI,EAAE,SAAS;AAG9B,YAAI,CAAC,KAAK,UAAU,CAAC,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC5C,gBAAM,IAAI,MAAM,4FAAiB;AAAA,QACnC;AAGA,cAAM,gBAAgB,MAAM,KAAK,SAAS,GAAG;AAG7C,cAAM,YAAY,cAAc;AAAA,UAAK,cACnC,SAAS,WAAW,KAAK,UACzB,SAAS,SAAS,KAAK,QACvB,SAAS,SAAS,KAAK;AAAA,QACzB;AAEA,YAAI,WAAW;AACb,gBAAM,IAAI,MAAM,sCAAQ;AAAA,QAC1B;AAEA,sBAAc,KAAK,IAAI;AAGvB,cAAM,IAAI,SAAS,IAAI,SAAS,KAAK,UAAU,aAAa,CAAC;AAE7D,eAAO,IAAI,SAAS,IAAI;AAAA,UACtB,QAAQ;AAAA,UACR,SAAS,EAAE,YAAY,IAAI;AAAA,QAC7B,CAAC;AAAA,MACH,SAAS,GAAP;AACA,gBAAQ,MAAM,yCAAW,CAAC;AAC1B,eAAO,KAAK,gBAAgB,SAAS,KAAK,yCAAW,EAAE,SAAS;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,iBAAiB,SAAS,MAAM,KAAK;AACzC,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,IAAI,SAAS,gBAAgB,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrD;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,YAAM,EAAE,GAAG,IAAI,MAAM,QAAQ,KAAK;AAClC,YAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,YAAM,gBAAgB,MAAM,OAAO,UAAQ,KAAK,OAAO,EAAE;AACzD,YAAM,IAAI,SAAS,IAAI,SAAS,KAAK,UAAU,aAAa,CAAC;AAC7D,aAAO,IAAI,SAAS,IAAI;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK,SAAS;AACrD,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,eAAe,UAAU,oBAAoB,KAAK;AAGxD,UAAM,YAAY,QAAQ,QAAQ,IAAI,YAAY,KAAK;AACvD,UAAM,SAAS,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AAChD,UAAM,YAAY,UAAU,SAAS,SAAS,KAAK,OAAO,SAAS,WAAW;AAE9E,QAAI,WAAW;AAEb,YAAM,iBAAiB,KAAK,YAAY;AACxC,YAAM,OAAO,KAAK,0BAA0B,SAAS,gBAAgB,OAAO,YAAY;AACxF,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,MACxD,CAAC;AAAA,IACH,OAAO;AAEL,aAAO,IAAI,SAAS,cAAc;AAAA,QAChC,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK,SAAS;AACrD,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,cAAc,UAAU,cAAc,KAAK;AAGjD,UAAM,cAAc,KAAK,WAAW,WAAW;AAG/C,UAAM,YAAY,QAAQ,QAAQ,IAAI,YAAY,KAAK;AACvD,UAAM,SAAS,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AAChD,UAAM,YAAY,UAAU,SAAS,SAAS,KAAK,OAAO,SAAS,WAAW;AAE9E,QAAI,WAAW;AAEb,YAAM,OAAO,KAAK,yBAAyB,SAAS,aAAa,KAAK;AACtE,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,MACxD,CAAC;AAAA,IACH,OAAO;AAEL,aAAO,IAAI,SAAS,aAAa;AAAA,QAC/B,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK,SAAS;AACrD,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,cAAc,KAAK,oBAAoB,KAAK;AAGlD,UAAM,YAAY,QAAQ,QAAQ,IAAI,YAAY,KAAK;AACvD,UAAM,SAAS,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AAChD,UAAM,YAAY,UAAU,SAAS,SAAS,KAAK,OAAO,SAAS,WAAW;AAE9E,QAAI,WAAW;AAEb,YAAM,OAAO,KAAK,yBAAyB,SAAS,aAAa,KAAK;AACtE,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,MACxD,CAAC;AAAA,IACH,OAAO;AAEL,aAAO,IAAI,SAAS,aAAa;AAAA,QAC/B,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,SAAS,KAAK;AAClB,QAAI;AACF,YAAM,YAAY,MAAM,IAAI,SAAS,IAAI,OAAO;AAChD,aAAO,YAAY,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,IAC9C,SAAS,GAAP;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGA,YAAY,eAAe,IAAI;AAC7B,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,OAAO,KAAK,aAAa;AAC7B,WAAO,KAAK,QAAQ,WAAW,GAAG;AAClC,WAAO,KAAK,QAAQ,qBAAqB,eACvC,kCAAkC,uBAAuB,EAAE;AAE7D,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,IACxD,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,gBAAgB,SAAS,KAAK,eAAe,IAAI;AACrD,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,UAAU,IAAI,IAAI,QAAQ,GAAG,EAAE;AAErC,QAAI,OAAO,KAAK,iBAAiB;AACjC,WAAO,KAAK,QAAQ,WAAW,GAAG;AAElC,WAAO,KAAK,QAAQ,qBAAqB,OAAO;AAChD,WAAO,KAAK,QAAQ,kBAAkB,KAAK,kBAAkB,KAAK,CAAC;AACnE,WAAO,KAAK,QAAQ,qBAAqB,eACvC,kCAAkC,uBAAuB,EAAE;AAE7D,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,IACxD,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,kBAAkB,OAAO;AACvB,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI,UAAQ;AAAA;AAAA;AAAA,oCAGO,KAAK,KAAK,YAAY;AAAA,4DACE,KAAK;AAAA;AAAA;AAAA,oBAG7C,KAAK;AAAA,YACb,KAAK,UAAU,KAAK;AAAA;AAAA;AAAA,KAG3B,EAAE,KAAK,EAAE;AAAA,EACZ;AAAA;AAAA,EAGA,oBAAoB,OAAO;AACzB,UAAM,UAAU,MAAM,IAAI,UAAQ;AAChC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,iBAAO,GAAG,KAAK,cAAc,KAAK,WAAW,KAAK,wBAAwB,KAAK,oBAAoB,KAAK;AAAA,QAC1G,KAAK;AACH,iBAAO,GAAG,KAAK,gBAAgB,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW,cAAc,KAAK,sBAAsB,KAAK,aAAa;AAAA,QACxI;AACE,iBAAO;AAAA,MACX;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AAEjB,WAAO;AAAA;AAAA;AAAA;AAAA,EAIT,QAAQ,KAAK,IAAI;AAAA;AAAA;AAAA,kBAGD,MAAM,IAAI,OAAK,EAAE,IAAI,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA;AAAA,EAGA,WAAW,KAAK,SAAS,GAAG;AAC1B,UAAM,SAAS,KAAK,OAAO,MAAM;AACjC,QAAI,OAAO;AAEX,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAW,QAAQ,KAAK;AACtB,YAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,kBAAQ,GAAG,WAAW,KAAK,WAAW,MAAM,SAAS,CAAC,EAAE,KAAK;AAAA;AAAA,QAC/D,OAAO;AACL,kBAAQ,GAAG,WAAW,KAAK,gBAAgB,IAAI;AAAA;AAAA,QACjD;AAAA,MACF;AAAA,IACF,WAAW,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAClD,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC9C,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,kBAAQ,GAAG,SAAS;AAAA;AACpB,kBAAQ,KAAK,WAAW,OAAO,SAAS,CAAC;AAAA,QAC3C,WAAW,OAAO,UAAU,YAAY,UAAU,MAAM;AACtD,kBAAQ,GAAG,SAAS;AAAA;AACpB,kBAAQ,KAAK,WAAW,OAAO,SAAS,CAAC;AAAA,QAC3C,OAAO;AACL,kBAAQ,GAAG,SAAS,QAAQ,KAAK,gBAAgB,KAAK;AAAA;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,gBAAgB,OAAO;AACrB,QAAI,OAAO,UAAU,UAAU;AAE7B,UAAI,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAChE,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAChE,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAChE,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,KAChE,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,GAAG;AAC9C,eAAO,IAAI,MAAM,QAAQ,MAAM,KAAK;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,yBAAyB,MAAM,SAAS,OAAO;AAC7C,UAAM,YAAY,MAAM;AACxB,UAAM,iBAAiB,QAAQ,SAAS,MAAO,QAAQ,UAAU,GAAG,GAAI,IAAI,QAAQ;AAEpF,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA,aAKE;AAAA;AAAA,UAEH,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAoED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAMsB;AAAA;AAAA;AAAA;AAAA,2CAIA,KAAK,MAAM,QAAQ,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA,2CAIhC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oHAQa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yDAWC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCASrB,QAAQ,QAAQ,MAAM,KAAK,EAAE,QAAQ,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBnF;AAAA;AAAA,EAGA,SAAS;AACP,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+OT;AAAA;AAAA,EAGA,eAAe;AACb,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BT;AAAA;AAAA,EAGA,mBAAmB;AACjB,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwLT;AACF;;;AC19BA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}