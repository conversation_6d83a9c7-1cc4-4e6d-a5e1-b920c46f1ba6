// 节点转换器

export class NodeConverter {
  constructor() {}

  // 解析节点链接
  parseNode(config, type) {
    try {
      switch (type) {
        case 'vmess':
          return this.parseVmess(config);
        case 'vless':
          return this.parseVless(config);
        case 'ss':
          return this.parseShadowsocks(config);
        case 'trojan':
          return this.parseTrojan(config);
        case 'http':
          return this.parseHttp(config);
        default:
          throw new Error('不支持的节点类型');
      }
    } catch (e) {
      throw new Error(`解析节点失败: ${e.message}`);
    }
  }

  // 解析VMess链接
  parseVmess(config) {
    if (config.startsWith('vmess://')) {
      const data = JSON.parse(atob(config.substring(8)));
      return {
        type: 'vmess',
        name: data.ps || 'VMess节点',
        server: data.add,
        port: parseInt(data.port),
        uuid: data.id,
        alterId: parseInt(data.aid) || 0,
        cipher: data.scy || 'auto',
        network: data.net || 'tcp',
        tls: data.tls === 'tls',
        path: data.path || '',
        host: data.host || ''
      };
    }
    throw new Error('无效的VMess链接');
  }

  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith('vless://')) {
      const url = new URL(config);
      return {
        type: 'vless',
        name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
        server: url.hostname,
        port: parseInt(url.port),
        uuid: url.username,
        encryption: url.searchParams.get('encryption') || 'none',
        network: url.searchParams.get('type') || 'tcp',
        tls: url.searchParams.get('security') === 'tls',
        path: url.searchParams.get('path') || '',
        host: url.searchParams.get('host') || ''
      };
    }
    throw new Error('无效的VLess链接');
  }

  // 解析Shadowsocks链接
  parseShadowsocks(config) {
    if (config.startsWith('ss://')) {
      const url = new URL(config);
      const userInfo = atob(url.username);
      const [method, password] = userInfo.split(':');
      
      return {
        type: 'ss',
        name: decodeURIComponent(url.hash.substring(1)) || 'SS节点',
        server: url.hostname,
        port: parseInt(url.port),
        method: method,
        password: password
      };
    }
    throw new Error('无效的Shadowsocks链接');
  }

  // 解析Trojan链接
  parseTrojan(config) {
    if (config.startsWith('trojan://')) {
      const url = new URL(config);
      return {
        type: 'trojan',
        name: decodeURIComponent(url.hash.substring(1)) || 'Trojan节点',
        server: url.hostname,
        port: parseInt(url.port),
        password: url.username,
        sni: url.searchParams.get('sni') || url.hostname
      };
    }
    throw new Error('无效的Trojan链接');
  }

  // 解析HTTP代理
  parseHttp(config) {
    if (config.startsWith('http://') || config.startsWith('https://')) {
      const url = new URL(config);
      return {
        type: 'http',
        name: `HTTP-${url.hostname}`,
        server: url.hostname,
        port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
        username: url.username || '',
        password: url.password || '',
        tls: url.protocol === 'https:'
      };
    }
    throw new Error('无效的HTTP链接');
  }

  // 转换为V2Ray订阅格式
  toV2raySubscription(nodes) {
    const configs = nodes.map(node => {
      switch (node.type) {
        case 'vmess':
          return this.nodeToVmessLink(node);
        case 'vless':
          return this.nodeToVlessLink(node);
        case 'ss':
          return this.nodeToSsLink(node);
        case 'trojan':
          return this.nodeToTrojanLink(node);
        default:
          return null;
      }
    }).filter(Boolean);
    
    return btoa(configs.join('\n'));
  }

  // 转换为Clash配置
  toClashConfig(nodes) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map(p => p.name);

    // 如果没有有效的代理节点，创建一个基础配置
    if (proxyNames.length === 0) {
      return {
        port: 7890,
        'socks-port': 7891,
        'allow-lan': false,
        mode: 'rule',
        'log-level': 'info',
        'external-controller': '127.0.0.1:9090',
        proxies: [],
        'proxy-groups': [
          {
            name: '🎯 全球直连',
            type: 'select',
            proxies: ['DIRECT']
          }
        ],
        rules: [
          'DOMAIN-SUFFIX,local,DIRECT',
          'IP-CIDR,*********/8,DIRECT',
          'IP-CIDR,**********/12,DIRECT',
          'IP-CIDR,***********/16,DIRECT',
          'IP-CIDR,10.0.0.0/8,DIRECT',
          'GEOIP,CN,🎯 全球直连',
          'MATCH,🎯 全球直连'
        ]
      };
    }

    // 有代理节点时的完整配置
    const proxyGroups = [
      {
        name: '🚀 节点选择',
        type: 'select',
        proxies: ['♻️ 自动选择', '🎯 全球直连'].concat(proxyNames)
      },
      {
        name: '🎯 全球直连',
        type: 'select',
        proxies: ['DIRECT']
      }
    ];

    // 只有在有多个节点时才添加自动选择组
    if (proxyNames.length > 1) {
      proxyGroups.splice(1, 0, {
        name: '♻️ 自动选择',
        type: 'url-test',
        proxies: proxyNames,
        url: 'http://www.gstatic.com/generate_204',
        interval: 300,
        tolerance: 50
      });
    }

    return {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: proxies,
      'proxy-groups': proxyGroups,
      rules: [
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        'GEOIP,CN,🎯 全球直连',
        'MATCH,🚀 节点选择'
      ]
    };
  }

  // 节点转VMess链接
  nodeToVmessLink(node) {
    const vmessObj = {
      v: '2',
      ps: node.name,
      add: node.server,
      port: node.port.toString(),
      id: node.uuid,
      aid: node.alterId.toString(),
      scy: node.cipher,
      net: node.network,
      type: 'none',
      host: node.host,
      path: node.path,
      tls: node.tls ? 'tls' : ''
    };
    return 'vmess://' + btoa(JSON.stringify(vmessObj));
  }

  // 节点转VLess链接
  nodeToVlessLink(node) {
    const params = new URLSearchParams();
    params.set('encryption', node.encryption);
    params.set('type', node.network);
    if (node.tls) params.set('security', 'tls');
    if (node.path) params.set('path', node.path);
    if (node.host) params.set('host', node.host);
    
    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }

  // 节点转SS链接
  nodeToSsLink(node) {
    const userInfo = btoa(`${node.method}:${node.password}`);
    return `ss://${userInfo}@${node.server}:${node.port}#${encodeURIComponent(node.name)}`;
  }

  // 节点转Trojan链接
  nodeToTrojanLink(node) {
    const params = new URLSearchParams();
    if (node.sni) params.set('sni', node.sni);
    
    return `trojan://${node.password}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    switch (node.type) {
      case 'vmess':
        const vmessProxy = {
          name: node.name,
          type: 'vmess',
          server: node.server,
          port: node.port,
          uuid: node.uuid,
          alterId: node.alterId || 0,
          cipher: node.cipher || 'auto'
        };

        // 添加TLS配置
        if (node.tls) {
          vmessProxy.tls = true;
        }

        // 添加网络配置
        if (node.network === 'ws') {
          vmessProxy.network = 'ws';
          vmessProxy['ws-opts'] = {
            path: node.path || '/',
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === 'h2') {
          vmessProxy.network = 'h2';
          vmessProxy['h2-opts'] = {
            host: [node.host || node.server],
            path: node.path || '/'
          };
        } else {
          vmessProxy.network = 'tcp';
        }

        return vmessProxy;

      case 'vless':
        const vlessProxy = {
          name: node.name,
          type: 'vless',
          server: node.server,
          port: node.port,
          uuid: node.uuid,
          flow: node.flow || '',
          'packet-encoding': 'xudp'
        };

        // 添加TLS配置
        if (node.tls) {
          vlessProxy.tls = true;
          if (node.sni) {
            vlessProxy.servername = node.sni;
          }
        }

        // 添加网络配置
        if (node.network === 'ws') {
          vlessProxy.network = 'ws';
          vlessProxy['ws-opts'] = {
            path: node.path || '/',
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === 'grpc') {
          vlessProxy.network = 'grpc';
          vlessProxy['grpc-opts'] = {
            'grpc-service-name': node.serviceName || 'GunService'
          };
        } else {
          vlessProxy.network = 'tcp';
        }

        return vlessProxy;

      case 'ss':
        return {
          name: node.name,
          type: 'ss',
          server: node.server,
          port: node.port,
          cipher: node.method,
          password: node.password,
          udp: true
        };

      case 'trojan':
        const trojanProxy = {
          name: node.name,
          type: 'trojan',
          server: node.server,
          port: node.port,
          password: node.password,
          udp: true
        };

        // 添加SNI配置
        if (node.sni) {
          trojanProxy.sni = node.sni;
        }

        // 添加网络配置
        if (node.network === 'ws') {
          trojanProxy.network = 'ws';
          trojanProxy['ws-opts'] = {
            path: node.path || '/',
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === 'grpc') {
          trojanProxy.network = 'grpc';
          trojanProxy['grpc-opts'] = {
            'grpc-service-name': node.serviceName || 'GunService'
          };
        }

        return trojanProxy;

      case 'http':
        const httpProxy = {
          name: node.name,
          type: 'http',
          server: node.server,
          port: node.port
        };

        if (node.username && node.password) {
          httpProxy.username = node.username;
          httpProxy.password = node.password;
        }

        if (node.tls) {
          httpProxy.tls = true;
        }

        return httpProxy;

      default:
        return null;
    }
  }
}
