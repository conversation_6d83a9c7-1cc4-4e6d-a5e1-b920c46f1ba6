/**
 * 修复后的节点转换器
 */

class NodeConverter {
  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith('vless://')) {
      try {
        const url = new URL(config);
        const params = url.searchParams;
        
        // 基础配置
        const node = {
          type: 'vless',
          name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
          server: url.hostname,
          port: parseInt(url.port),
          uuid: url.username,
          encryption: params.get('encryption') || 'none',
          network: params.get('type') || 'tcp'
        };

        // 验证必要字段
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('VLess链接缺少必要参数');
        }

        // 安全传输配置
        const security = params.get('security');
        if (security === 'tls') {
          node.tls = true;
          node.sni = params.get('sni') || url.hostname;
          node.alpn = params.get('alpn');
          node.fingerprint = params.get('fp');
        } else if (security === 'reality') {
          node.reality = true;
          node.sni = params.get('sni') || url.hostname;
          node.fingerprint = params.get('fp') || 'chrome';
          node.publicKey = params.get('pbk');
          node.shortId = params.get('sid');
          node.spiderX = params.get('spx');
        }

        // Flow控制 (XTLS)
        const flow = params.get('flow');
        if (flow) {
          node.flow = flow;
        }

        // 传输协议配置
        switch (node.network) {
          case 'tcp':
            node.headerType = params.get('headerType') || 'none';
            break;
          case 'ws':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
          case 'grpc':
            node.serviceName = params.get('serviceName') || 'GunService';
            node.mode = params.get('mode') || 'gun';
            break;
          case 'h2':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
        }

        return node;
      } catch (error) {
        throw new Error(`VLess链接解析失败: ${error.message}`);
      }
    }
    throw new Error('无效的VLess链接');
  }

  // 解析节点
  parseNode(config, type) {
    switch (type) {
      case 'vless':
        return this.parseVless(config);
      default:
        throw new Error(`不支持的节点类型: ${type}`);
    }
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    if (node.type === 'vless') {
      const vlessProxy = {
        name: node.name,
        type: 'vless',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        cipher: '',
        alterId: 0,
        udp: true,
        tls: true,
        'skip-cert-verify': false
      };

      // 添加服务器名称
      if (node.sni) {
        vlessProxy.servername = node.sni;
      }

      // 添加网络类型
      vlessProxy.network = node.network || 'tcp';

      // 添加Flow控制 (XTLS)
      if (node.flow) {
        vlessProxy.flow = node.flow;
      }

      // 添加客户端指纹
      if (node.fingerprint) {
        vlessProxy['client-fingerprint'] = node.fingerprint;
      }

      // 添加REALITY配置 - 只有公钥时才添加
      if (node.reality && node.publicKey) {
        vlessProxy['reality-opts'] = {
          'public-key': node.publicKey
        };
      }

      // 添加网络配置选项
      if (node.network === 'ws') {
        vlessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'grpc') {
        vlessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'h2') {
        vlessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      }

      return vlessProxy;
    }
    return null;
  }

  // 转换为Clash配置
  toClashConfig(nodes) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map(p => p.name);

    return {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'Rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      'unified-delay': true,
      hosts: {
        'time.facebook.com': '*************',
        'time.android.com': '*************'
      },
      dns: {
        enable: true,
        ipv6: false,
        'default-nameserver': ['*********', '*********'],
        nameserver: ['*******', '*******'],
        fallback: ['tls://*******:853', 'tls://*******:853'],
        'fallback-filter': {
          geoip: true,
          'geoip-code': 'CN',
          ipcidr: ['240.0.0.0/4']
        },
        'enhanced-mode': 'fake-ip',
        'fake-ip-range': '**********/16',
        'fake-ip-filter': [
          '*.lan',
          'localhost.ptlogin2.qq.com',
          'dns.msftncsi.com',
          'www.msftncsi.com',
          'www.msftconnecttest.com'
        ]
      },
      proxies: proxies,
      'proxy-groups': [
        {
          name: '🔰 选择节点',
          type: 'select',
          proxies: proxyNames.length > 0 ? proxyNames : ['DIRECT']
        },
        {
          name: '🌏 爱奇艺&哔哩哔哩',
          type: 'select',
          proxies: ['DIRECT']
        },
        {
          name: '📺 动画疯',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['🔰 选择节点'] : ['DIRECT']
        },
        {
          name: '🎮 Steam 登录/下载',
          type: 'select',
          proxies: ['DIRECT']
        },
        {
          name: '🎮 Steam 商店/社区',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['🔰 选择节点'] : ['DIRECT']
        },
        {
          name: '🌩️ Cloudflare',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['🔰 选择节点', 'DIRECT'] : ['DIRECT']
        },
        {
          name: '☁️ OneDrive',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['🔰 选择节点'] : ['DIRECT']
        },
        {
          name: '🇨🇳 国内网站',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['DIRECT', '🔰 选择节点'] : ['DIRECT']
        },
        {
          name: '🛑 拦截广告',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['REJECT', 'DIRECT', '🔰 选择节点'] : ['REJECT', 'DIRECT']
        },
        {
          name: '🐟 漏网之鱼',
          type: 'select',
          proxies: proxyNames.length > 0 ? ['🔰 选择节点', 'DIRECT'] : ['DIRECT']
        }
      ],
      rules: [
        // 广告拦截
        'DOMAIN-SUFFIX,googlesyndication.com,🛑 拦截广告',
        'DOMAIN-SUFFIX,googleadservices.com,🛑 拦截广告',
        'DOMAIN-SUFFIX,doubleclick.net,🛑 拦截广告',
        
        // 爱奇艺&哔哩哔哩
        'DOMAIN-SUFFIX,iqiyi.com,🌏 爱奇艺&哔哩哔哩',
        'DOMAIN-SUFFIX,bilibili.com,🌏 爱奇艺&哔哩哔哩',
        'DOMAIN-SUFFIX,hdslb.com,🌏 爱奇艺&哔哩哔哩',
        
        // 动画疯
        'DOMAIN-SUFFIX,ani.gamer.com.tw,📺 动画疯',
        'DOMAIN-SUFFIX,bahamut.com.tw,📺 动画疯',
        
        // Steam
        'DOMAIN-SUFFIX,steampowered.com,🎮 Steam 登录/下载',
        'DOMAIN-SUFFIX,steamcommunity.com,🎮 Steam 商店/社区',
        'DOMAIN-SUFFIX,steamstatic.com,🎮 Steam 商店/社区',
        
        // Cloudflare
        'DOMAIN-SUFFIX,cloudflare.com,🌩️ Cloudflare',
        'DOMAIN-SUFFIX,cloudflarestream.com,🌩️ Cloudflare',
        
        // OneDrive
        'DOMAIN-SUFFIX,onedrive.live.com,☁️ OneDrive',
        'DOMAIN-SUFFIX,1drv.ms,☁️ OneDrive',
        
        // 国内网站
        'DOMAIN-SUFFIX,qq.com,🇨🇳 国内网站',
        'DOMAIN-SUFFIX,taobao.com,🇨🇳 国内网站',
        'DOMAIN-SUFFIX,tmall.com,🇨🇳 国内网站',
        'DOMAIN-SUFFIX,jd.com,🇨🇳 国内网站',
        'DOMAIN-SUFFIX,weibo.com,🇨🇳 国内网站',
        'DOMAIN-SUFFIX,zhihu.com,🇨🇳 国内网站',
        
        // 本地网络
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        
        // 中国IP
        'GEOIP,CN,🇨🇳 国内网站',
        
        // 兜底规则
        'MATCH,🐟 漏网之鱼'
      ]
    };
  }

  // 转换为V2Ray订阅
  toV2raySubscription(nodes) {
    const configs = nodes.map(node => {
      if (node.type === 'vless') {
        return this.nodeToVlessLink(node);
      }
      return null;
    }).filter(Boolean);
    
    return btoa(configs.join('\n'));
  }

  // 节点转VLess链接
  nodeToVlessLink(node) {
    const params = new URLSearchParams();
    params.set('encryption', node.encryption || 'none');
    params.set('type', node.network || 'tcp');
    
    // 安全传输配置
    if (node.reality) {
      params.set('security', 'reality');
      if (node.sni) params.set('sni', node.sni);
      if (node.fingerprint) params.set('fp', node.fingerprint);
      if (node.publicKey) params.set('pbk', node.publicKey);
      if (node.shortId) params.set('sid', node.shortId);
    } else if (node.tls) {
      params.set('security', 'tls');
      if (node.sni) params.set('sni', node.sni);
      if (node.alpn) params.set('alpn', node.alpn);
      if (node.fingerprint) params.set('fp', node.fingerprint);
    }
    
    // Flow控制
    if (node.flow) params.set('flow', node.flow);
    
    // 传输协议配置
    switch (node.network) {
      case 'ws':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
      case 'grpc':
        if (node.serviceName) params.set('serviceName', node.serviceName);
        if (node.mode) params.set('mode', node.mode);
        break;
      case 'h2':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
    }
    
    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }
}

module.exports = { NodeConverter };
