var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-cANl8R/strip-cf-connecting-ip-header.js
function stripCfConnectingIPHeader(input, init) {
  const request = new Request(input, init);
  request.headers.delete("CF-Connecting-IP");
  return request;
}
__name(stripCfConnectingIPHeader, "stripCfConnectingIPHeader");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    return Reflect.apply(target, thisArg, [
      stripCfConnectingIPHeader.apply(null, argArray)
    ]);
  }
});

// src/auth.js
var Auth = class {
  constructor(env) {
    this.env = env;
  }
  // 验证用户凭据
  validateCredentials(username, password) {
    return username === this.env.ADMIN_USERNAME && password === this.env.ADMIN_PASSWORD;
  }
  // 生成JWT token (简化版)
  generateToken(username) {
    const payload = {
      username,
      exp: Date.now() + 24 * 60 * 60 * 1e3
      // 24小时过期
    };
    return btoa(JSON.stringify(payload));
  }
  // 验证token
  validateToken(token) {
    try {
      const payload = JSON.parse(atob(token));
      if (payload.exp < Date.now()) {
        return false;
      }
      return payload.username === this.env.ADMIN_USERNAME;
    } catch (e) {
      return false;
    }
  }
  // 从请求中获取token
  getTokenFromRequest(request) {
    const cookie = request.headers.get("Cookie");
    if (!cookie)
      return null;
    const match = cookie.match(/auth_token=([^;]+)/);
    return match ? match[1] : null;
  }
  // 检查是否已认证
  isAuthenticated(request) {
    const token = this.getTokenFromRequest(request);
    return token && this.validateToken(token);
  }
  // 创建认证cookie
  createAuthCookie(token) {
    return `auth_token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=86400; Path=/`;
  }
  // 创建登出cookie
  createLogoutCookie() {
    return `auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/`;
  }
};
__name(Auth, "Auth");

// src/converter.js
var NodeConverter = class {
  constructor() {
  }
  // 解析节点链接
  parseNode(config, type) {
    try {
      switch (type) {
        case "vmess":
          return this.parseVmess(config);
        case "vless":
          return this.parseVless(config);
        case "ss":
          return this.parseShadowsocks(config);
        case "trojan":
          return this.parseTrojan(config);
        case "http":
          return this.parseHttp(config);
        default:
          throw new Error("\u4E0D\u652F\u6301\u7684\u8282\u70B9\u7C7B\u578B");
      }
    } catch (e) {
      throw new Error(`\u89E3\u6790\u8282\u70B9\u5931\u8D25: ${e.message}`);
    }
  }
  // 解析VMess链接
  parseVmess(config) {
    if (config.startsWith("vmess://")) {
      const data = JSON.parse(atob(config.substring(8)));
      return {
        type: "vmess",
        name: data.ps || "VMess\u8282\u70B9",
        server: data.add,
        port: parseInt(data.port),
        uuid: data.id,
        alterId: parseInt(data.aid) || 0,
        cipher: data.scy || "auto",
        network: data.net || "tcp",
        tls: data.tls === "tls",
        path: data.path || "",
        host: data.host || ""
      };
    }
    throw new Error("\u65E0\u6548\u7684VMess\u94FE\u63A5");
  }
  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith("vless://")) {
      const url = new URL(config);
      return {
        type: "vless",
        name: decodeURIComponent(url.hash.substring(1)) || "VLess\u8282\u70B9",
        server: url.hostname,
        port: parseInt(url.port),
        uuid: url.username,
        encryption: url.searchParams.get("encryption") || "none",
        network: url.searchParams.get("type") || "tcp",
        tls: url.searchParams.get("security") === "tls",
        path: url.searchParams.get("path") || "",
        host: url.searchParams.get("host") || ""
      };
    }
    throw new Error("\u65E0\u6548\u7684VLess\u94FE\u63A5");
  }
  // 解析Shadowsocks链接
  parseShadowsocks(config) {
    if (config.startsWith("ss://")) {
      const url = new URL(config);
      const userInfo = atob(url.username);
      const [method, password] = userInfo.split(":");
      return {
        type: "ss",
        name: decodeURIComponent(url.hash.substring(1)) || "SS\u8282\u70B9",
        server: url.hostname,
        port: parseInt(url.port),
        method,
        password
      };
    }
    throw new Error("\u65E0\u6548\u7684Shadowsocks\u94FE\u63A5");
  }
  // 解析Trojan链接
  parseTrojan(config) {
    if (config.startsWith("trojan://")) {
      const url = new URL(config);
      return {
        type: "trojan",
        name: decodeURIComponent(url.hash.substring(1)) || "Trojan\u8282\u70B9",
        server: url.hostname,
        port: parseInt(url.port),
        password: url.username,
        sni: url.searchParams.get("sni") || url.hostname
      };
    }
    throw new Error("\u65E0\u6548\u7684Trojan\u94FE\u63A5");
  }
  // 解析HTTP代理
  parseHttp(config) {
    if (config.startsWith("http://") || config.startsWith("https://")) {
      const url = new URL(config);
      return {
        type: "http",
        name: `HTTP-${url.hostname}`,
        server: url.hostname,
        port: parseInt(url.port) || (url.protocol === "https:" ? 443 : 80),
        username: url.username || "",
        password: url.password || "",
        tls: url.protocol === "https:"
      };
    }
    throw new Error("\u65E0\u6548\u7684HTTP\u94FE\u63A5");
  }
  // 转换为V2Ray订阅格式
  toV2raySubscription(nodes) {
    const configs = nodes.map((node) => {
      switch (node.type) {
        case "vmess":
          return this.nodeToVmessLink(node);
        case "vless":
          return this.nodeToVlessLink(node);
        case "ss":
          return this.nodeToSsLink(node);
        case "trojan":
          return this.nodeToTrojanLink(node);
        default:
          return null;
      }
    }).filter(Boolean);
    return btoa(configs.join("\n"));
  }
  // 转换为Clash配置
  toClashConfig(nodes) {
    const proxies = nodes.map((node) => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map((p) => p.name);
    if (proxyNames.length === 0) {
      return {
        port: 7890,
        "socks-port": 7891,
        "allow-lan": false,
        mode: "rule",
        "log-level": "info",
        "external-controller": "127.0.0.1:9090",
        proxies: [],
        "proxy-groups": [
          {
            name: "\u{1F3AF} \u5168\u7403\u76F4\u8FDE",
            type: "select",
            proxies: ["DIRECT"]
          }
        ],
        rules: [
          "DOMAIN-SUFFIX,local,DIRECT",
          "IP-CIDR,*********/8,DIRECT",
          "IP-CIDR,**********/12,DIRECT",
          "IP-CIDR,***********/16,DIRECT",
          "IP-CIDR,10.0.0.0/8,DIRECT",
          "GEOIP,CN,\u{1F3AF} \u5168\u7403\u76F4\u8FDE",
          "MATCH,\u{1F3AF} \u5168\u7403\u76F4\u8FDE"
        ]
      };
    }
    const proxyGroups = [
      {
        name: "\u{1F680} \u8282\u70B9\u9009\u62E9",
        type: "select",
        proxies: ["\u267B\uFE0F \u81EA\u52A8\u9009\u62E9", "\u{1F3AF} \u5168\u7403\u76F4\u8FDE"].concat(proxyNames)
      },
      {
        name: "\u{1F3AF} \u5168\u7403\u76F4\u8FDE",
        type: "select",
        proxies: ["DIRECT"]
      }
    ];
    if (proxyNames.length > 1) {
      proxyGroups.splice(1, 0, {
        name: "\u267B\uFE0F \u81EA\u52A8\u9009\u62E9",
        type: "url-test",
        proxies: proxyNames,
        url: "http://www.gstatic.com/generate_204",
        interval: 300,
        tolerance: 50
      });
    }
    return {
      port: 7890,
      "socks-port": 7891,
      "allow-lan": false,
      mode: "rule",
      "log-level": "info",
      "external-controller": "127.0.0.1:9090",
      proxies,
      "proxy-groups": proxyGroups,
      rules: [
        "DOMAIN-SUFFIX,local,DIRECT",
        "IP-CIDR,*********/8,DIRECT",
        "IP-CIDR,**********/12,DIRECT",
        "IP-CIDR,***********/16,DIRECT",
        "IP-CIDR,10.0.0.0/8,DIRECT",
        "GEOIP,CN,\u{1F3AF} \u5168\u7403\u76F4\u8FDE",
        "MATCH,\u{1F680} \u8282\u70B9\u9009\u62E9"
      ]
    };
  }
  // 节点转VMess链接
  nodeToVmessLink(node) {
    const vmessObj = {
      v: "2",
      ps: node.name,
      add: node.server,
      port: node.port.toString(),
      id: node.uuid,
      aid: node.alterId.toString(),
      scy: node.cipher,
      net: node.network,
      type: "none",
      host: node.host,
      path: node.path,
      tls: node.tls ? "tls" : ""
    };
    return "vmess://" + btoa(JSON.stringify(vmessObj));
  }
  // 节点转VLess链接
  nodeToVlessLink(node) {
    const params = new URLSearchParams();
    params.set("encryption", node.encryption);
    params.set("type", node.network);
    if (node.tls)
      params.set("security", "tls");
    if (node.path)
      params.set("path", node.path);
    if (node.host)
      params.set("host", node.host);
    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }
  // 节点转SS链接
  nodeToSsLink(node) {
    const userInfo = btoa(`${node.method}:${node.password}`);
    return `ss://${userInfo}@${node.server}:${node.port}#${encodeURIComponent(node.name)}`;
  }
  // 节点转Trojan链接
  nodeToTrojanLink(node) {
    const params = new URLSearchParams();
    if (node.sni)
      params.set("sni", node.sni);
    return `trojan://${node.password}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }
  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    switch (node.type) {
      case "vmess":
        const vmessProxy = {
          name: node.name,
          type: "vmess",
          server: node.server,
          port: node.port,
          uuid: node.uuid,
          alterId: node.alterId || 0,
          cipher: node.cipher || "auto"
        };
        if (node.tls) {
          vmessProxy.tls = true;
        }
        if (node.network === "ws") {
          vmessProxy.network = "ws";
          vmessProxy["ws-opts"] = {
            path: node.path || "/",
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === "h2") {
          vmessProxy.network = "h2";
          vmessProxy["h2-opts"] = {
            host: [node.host || node.server],
            path: node.path || "/"
          };
        } else {
          vmessProxy.network = "tcp";
        }
        return vmessProxy;
      case "vless":
        const vlessProxy = {
          name: node.name,
          type: "vless",
          server: node.server,
          port: node.port,
          uuid: node.uuid,
          flow: node.flow || "",
          "packet-encoding": "xudp"
        };
        if (node.tls) {
          vlessProxy.tls = true;
          if (node.sni) {
            vlessProxy.servername = node.sni;
          }
        }
        if (node.network === "ws") {
          vlessProxy.network = "ws";
          vlessProxy["ws-opts"] = {
            path: node.path || "/",
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === "grpc") {
          vlessProxy.network = "grpc";
          vlessProxy["grpc-opts"] = {
            "grpc-service-name": node.serviceName || "GunService"
          };
        } else {
          vlessProxy.network = "tcp";
        }
        return vlessProxy;
      case "ss":
        return {
          name: node.name,
          type: "ss",
          server: node.server,
          port: node.port,
          cipher: node.method,
          password: node.password,
          udp: true
        };
      case "trojan":
        const trojanProxy = {
          name: node.name,
          type: "trojan",
          server: node.server,
          port: node.port,
          password: node.password,
          udp: true
        };
        if (node.sni) {
          trojanProxy.sni = node.sni;
        }
        if (node.network === "ws") {
          trojanProxy.network = "ws";
          trojanProxy["ws-opts"] = {
            path: node.path || "/",
            headers: node.host ? { Host: node.host } : {}
          };
        } else if (node.network === "grpc") {
          trojanProxy.network = "grpc";
          trojanProxy["grpc-opts"] = {
            "grpc-service-name": node.serviceName || "GunService"
          };
        }
        return trojanProxy;
      case "http":
        const httpProxy = {
          name: node.name,
          type: "http",
          server: node.server,
          port: node.port
        };
        if (node.username && node.password) {
          httpProxy.username = node.username;
          httpProxy.password = node.password;
        }
        if (node.tls) {
          httpProxy.tls = true;
        }
        return httpProxy;
      default:
        return null;
    }
  }
};
__name(NodeConverter, "NodeConverter");

// src/index.js
var src_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const auth = new Auth(env);
    const converter = new NodeConverter();
    switch (url.pathname) {
      case "/":
        return this.handleHome(request, auth, env);
      case "/login":
        return this.handleLogin(request, auth, env);
      case "/logout":
        return this.handleLogout(request, auth);
      case "/add-node":
        return this.handleAddNode(request, auth, converter, env);
      case "/delete-node":
        return this.handleDeleteNode(request, auth, env);
      case "/sub/v2ray":
        return this.handleV2raySubscription(converter, env);
      case "/sub/clash":
        return this.handleClashSubscription(converter, env);
      case "/sub/surge":
        return this.handleSurgeSubscription(converter, env);
      default:
        return new Response("Not Found", { status: 404 });
    }
  },
  // 处理首页
  async handleHome(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return this.renderLogin();
    }
    return this.renderDashboard(request, env);
  },
  // 处理登录
  async handleLogin(request, auth, env) {
    if (request.method === "GET") {
      return this.renderLogin();
    }
    if (request.method === "POST") {
      const formData = await request.formData();
      const username = formData.get("username");
      const password = formData.get("password");
      if (auth.validateCredentials(username, password)) {
        const token = auth.generateToken(username);
        const response = new Response("", {
          status: 302,
          headers: {
            "Location": "/",
            "Set-Cookie": auth.createAuthCookie(token)
          }
        });
        return response;
      } else {
        return this.renderLogin("\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF");
      }
    }
  },
  // 处理登出
  async handleLogout(request, auth) {
    return new Response("", {
      status: 302,
      headers: {
        "Location": "/",
        "Set-Cookie": auth.createLogoutCookie()
      }
    });
  },
  // 处理添加节点
  async handleAddNode(request, auth, converter, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response("Unauthorized", { status: 401 });
    }
    if (request.method === "POST") {
      try {
        const formData = await request.formData();
        const type = formData.get("type");
        const name = formData.get("name");
        const config = formData.get("config");
        const node = converter.parseNode(config, type);
        node.name = name;
        node.id = Date.now().toString();
        const existingNodes = await this.getNodes(env);
        existingNodes.push(node);
        await env.NODES_KV.put("nodes", JSON.stringify(existingNodes));
        return new Response("", {
          status: 302,
          headers: { "Location": "/" }
        });
      } catch (e) {
        return this.renderDashboard(request, env, `\u6DFB\u52A0\u8282\u70B9\u5931\u8D25: ${e.message}`);
      }
    }
  },
  // 处理删除节点
  async handleDeleteNode(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response("Unauthorized", { status: 401 });
    }
    if (request.method === "POST") {
      const { id } = await request.json();
      const nodes = await this.getNodes(env);
      const filteredNodes = nodes.filter((node) => node.id !== id);
      await env.NODES_KV.put("nodes", JSON.stringify(filteredNodes));
      return new Response("OK");
    }
  },
  // 处理V2Ray订阅
  async handleV2raySubscription(converter, env) {
    const nodes = await this.getNodes(env);
    const subscription = converter.toV2raySubscription(nodes);
    return new Response(subscription, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Content-Disposition": 'attachment; filename="v2ray.txt"'
      }
    });
  },
  // 处理Clash订阅
  async handleClashSubscription(converter, env) {
    const nodes = await this.getNodes(env);
    const clashConfig = converter.toClashConfig(nodes);
    const yamlContent = this.jsonToYaml(clashConfig);
    return new Response(yamlContent, {
      headers: {
        "Content-Type": "text/yaml; charset=utf-8",
        "Content-Disposition": 'attachment; filename="clash.yaml"'
      }
    });
  },
  // 处理Surge订阅
  async handleSurgeSubscription(converter, env) {
    const nodes = await this.getNodes(env);
    const surgeConfig = this.generateSurgeConfig(nodes);
    return new Response(surgeConfig, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Content-Disposition": 'attachment; filename="surge.conf"'
      }
    });
  },
  // 获取节点列表
  async getNodes(env) {
    try {
      const nodesData = await env.NODES_KV.get("nodes");
      return nodesData ? JSON.parse(nodesData) : [];
    } catch (e) {
      return [];
    }
  },
  // 渲染登录页面
  renderLogin(errorMessage = "") {
    const css = this.getCSS();
    let html = this.getLoginHTML();
    html = html.replace("{{CSS}}", css);
    html = html.replace("{{ERROR_MESSAGE}}", errorMessage ? `<div class="alert alert-error">${errorMessage}</div>` : "");
    return new Response(html, {
      headers: { "Content-Type": "text/html; charset=utf-8" }
    });
  },
  // 渲染管理面板
  async renderDashboard(request, env, errorMessage = "") {
    const css = this.getCSS();
    const nodes = await this.getNodes(env);
    const baseUrl = new URL(request.url).origin;
    let html = this.getDashboardHTML();
    html = html.replace("{{CSS}}", css);
    html = html.replace(/\{\{BASE_URL\}\}/g, baseUrl);
    html = html.replace("{{NODES_LIST}}", this.generateNodesList(nodes));
    return new Response(html, {
      headers: { "Content-Type": "text/html; charset=utf-8" }
    });
  },
  // 生成节点列表HTML
  generateNodesList(nodes) {
    if (nodes.length === 0) {
      return "<p>\u6682\u65E0\u8282\u70B9</p>";
    }
    return nodes.map((node) => `
      <div class="node-item">
        <div class="node-header">
          <span class="node-type">${node.type.toUpperCase()}</span>
          <button class="btn-danger" onclick="deleteNode('${node.id}')">\u5220\u9664</button>
        </div>
        <div class="node-details">
          <strong>${node.name}</strong><br>
          ${node.server}:${node.port}
        </div>
      </div>
    `).join("");
  },
  // 生成Surge配置
  generateSurgeConfig(nodes) {
    const proxies = nodes.map((node) => {
      switch (node.type) {
        case "ss":
          return `${node.name} = ss, ${node.server}, ${node.port}, encrypt-method=${node.method}, password=${node.password}`;
        case "http":
          return `${node.name} = http, ${node.server}, ${node.port}${node.username ? `, username=${node.username}, password=${node.password}` : ""}`;
        default:
          return null;
      }
    }).filter(Boolean);
    return `[General]
skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, localhost, *.local

[Proxy]
${proxies.join("\n")}

[Proxy Group]
Proxy = select, ${nodes.map((n) => n.name).join(", ")}

[Rule]
FINAL,Proxy`;
  },
  // 简单的JSON到YAML转换器
  jsonToYaml(obj, indent = 0) {
    const spaces = "  ".repeat(indent);
    let yaml = "";
    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === "object" && item !== null) {
          yaml += `${spaces}- ${this.jsonToYaml(item, indent + 1).trim()}
`;
        } else {
          yaml += `${spaces}- ${this.escapeYamlValue(item)}
`;
        }
      }
    } else if (typeof obj === "object" && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          yaml += `${spaces}${key}:
`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else if (typeof value === "object" && value !== null) {
          yaml += `${spaces}${key}:
`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else {
          yaml += `${spaces}${key}: ${this.escapeYamlValue(value)}
`;
        }
      }
    }
    return yaml;
  },
  // 转义YAML值
  escapeYamlValue(value) {
    if (typeof value === "string") {
      if (value.includes(":") || value.includes("#") || value.includes("[") || value.includes("]") || value.includes("{") || value.includes("}") || value.includes("|") || value.includes(">") || value.includes("&") || value.includes("*") || value.includes("!") || value.includes("%") || value.includes("@") || value.includes("`")) {
        return `"${value.replace(/"/g, '\\"')}"`;
      }
      return value;
    }
    return value;
  },
  // 获取CSS内容
  getCSS() {
    return `* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* \u767B\u5F55\u9875\u9762\u6837\u5F0F */
.login-body {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
}

.container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* \u7BA1\u7406\u9762\u677F\u6837\u5F0F */
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  min-height: 100vh;
}

/* \u54CD\u5E94\u5F0F\u7F51\u683C\u5E03\u5C40 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

h1, h2 {
  text-align: center;
  color: #333;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #555;
  font-weight: 500;
}

input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #667eea;
}

button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: transform 0.2s;
}

button:hover {
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  margin-top: 0.5rem;
}

.btn-danger {
  background: #dc3545;
  width: auto;
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.node-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.node-type {
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
}

.node-details {
  font-size: 0.9rem;
  color: #666;
}

.subscription-links {
  background: #e9ecef;
  border-radius: 5px;
  padding: 1rem;
  margin-top: 2rem;
}

.link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-radius: 5px;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.link-item span {
  font-weight: 500;
  color: #333;
  min-width: 100px;
}

.copy-btn {
  background: #28a745;
  width: auto;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  min-width: 80px;
  flex-shrink: 0;
}

/* \u79FB\u52A8\u7AEF\u4F18\u5316 */
@media (max-width: 768px) {
  .container {
    padding: 1.5rem;
    margin: 0.5rem;
    max-width: none;
  }

  .dashboard-container {
    padding: 0.5rem;
  }

  .link-item {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .link-item span {
    margin-bottom: 0.5rem;
    min-width: auto;
  }

  .copy-btn {
    width: 100%;
    min-width: auto;
  }

  .node-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .btn-danger {
    margin-left: 0;
    width: 100%;
  }
}

.alert {
  padding: 0.75rem;
  margin-bottom: 1rem;
  border-radius: 5px;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}`;
  },
  // 获取登录页面HTML
  getLoginHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>\u8BA2\u9605\u8F6C\u6362\u5668 - \u767B\u5F55</title>
    <style>{{CSS}}</style>
</head>
<body class="login-body">
    <div class="container">
        <h1>\u{1F680} \u8BA2\u9605\u8F6C\u6362\u5668</h1>
        <p style="text-align: center; color: #666; margin-bottom: 2rem;">\u5B89\u5168\u7684\u8282\u70B9\u8BA2\u9605\u7BA1\u7406\u5E73\u53F0</p>
        {{ERROR_MESSAGE}}
        <form method="POST" action="/login">
            <div class="form-group">
                <label for="username">\u{1F464} \u7528\u6237\u540D:</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            <div class="form-group">
                <label for="password">\u{1F512} \u5BC6\u7801:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            <button type="submit">\u767B\u5F55</button>
        </form>
    </div>
</body>
</html>`;
  },
  // 获取管理面板HTML
  getDashboardHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>\u8BA2\u9605\u8F6C\u6362\u5668 - \u7BA1\u7406\u9762\u677F</title>
    <style>{{CSS}}</style>
</head>
<body>
    <div class="dashboard-container">
        <header style="text-align: center; margin-bottom: 2rem;">
            <h1>\u{1F680} \u8282\u70B9\u7BA1\u7406\u9762\u677F</h1>
            <p style="color: #666;">\u7BA1\u7406\u4F60\u7684\u4EE3\u7406\u8282\u70B9\u548C\u8BA2\u9605\u94FE\u63A5</p>
        </header>

        <div class="dashboard-grid">
            <!-- \u6DFB\u52A0\u8282\u70B9\u8868\u5355 -->
            <div class="container">
                <h2>\u2795 \u6DFB\u52A0\u65B0\u8282\u70B9</h2>
                <form method="POST" action="/add-node">
                    <div class="form-group">
                        <label for="node-type">\u{1F527} \u8282\u70B9\u7C7B\u578B:</label>
                        <select id="node-type" name="type" required>
                            <option value="">\u9009\u62E9\u8282\u70B9\u7C7B\u578B</option>
                            <option value="vmess">VMess</option>
                            <option value="vless">VLess</option>
                            <option value="ss">Shadowsocks</option>
                            <option value="trojan">Trojan</option>
                            <option value="http">HTTP/HTTPS</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="node-name">\u{1F4DD} \u8282\u70B9\u540D\u79F0:</label>
                        <input type="text" id="node-name" name="name" required placeholder="\u4F8B\u5982: \u9999\u6E2F\u8282\u70B901">
                    </div>
                    <div class="form-group">
                        <label for="node-config">\u2699\uFE0F \u8282\u70B9\u914D\u7F6E:</label>
                        <textarea id="node-config" name="config" rows="4" required placeholder="\u7C98\u8D34\u8282\u70B9\u94FE\u63A5\u6216\u914D\u7F6E\u4FE1\u606F"></textarea>
                    </div>
                    <button type="submit">\u6DFB\u52A0\u8282\u70B9</button>
                </form>
            </div>

            <!-- \u8BA2\u9605\u94FE\u63A5 -->
            <div class="container subscription-links">
                <h2>\u{1F4CB} \u8BA2\u9605\u94FE\u63A5</h2>
                <div class="link-item">
                    <span>\u{1F4F1} V2Ray\u8BA2\u9605:</span>
                    <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/v2ray')">\u590D\u5236\u94FE\u63A5</button>
                </div>
                <div class="link-item">
                    <span>\u2694\uFE0F Clash\u8BA2\u9605:</span>
                    <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/clash')">\u590D\u5236\u94FE\u63A5</button>
                </div>
                <div class="link-item">
                    <span>\u{1F30A} Surge\u8BA2\u9605:</span>
                    <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/surge')">\u590D\u5236\u94FE\u63A5</button>
                </div>
                <div style="margin-top: 1rem; padding: 0.75rem; background: #e3f2fd; border-radius: 5px; font-size: 0.9rem; color: #1565c0;">
                    \u{1F4A1} \u63D0\u793A: \u590D\u5236\u8BA2\u9605\u94FE\u63A5\u5230\u4F60\u7684\u4EE3\u7406\u5BA2\u6237\u7AEF\u4E2D\u4F7F\u7528
                </div>
            </div>
        </div>

        <!-- \u8282\u70B9\u5217\u8868 -->
        <div class="container" style="margin-top: 1.5rem;">
            <h2>\u{1F4CA} \u5DF2\u6DFB\u52A0\u7684\u8282\u70B9</h2>
            {{NODES_LIST}}
        </div>

        <!-- \u9000\u51FA\u767B\u5F55 -->
        <div class="container" style="margin-top: 1.5rem; text-align: center;">
            <form method="POST" action="/logout">
                <button type="submit" class="btn-secondary">\u{1F6AA} \u9000\u51FA\u767B\u5F55</button>
            </form>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('\u2705 \u94FE\u63A5\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F', 'success');
            }).catch(function(err) {
                console.error('\u590D\u5236\u5931\u8D25:', err);
                showNotification('\u274C \u590D\u5236\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u590D\u5236', 'error');
                // \u964D\u7EA7\u65B9\u6848\uFF1A\u663E\u793A\u94FE\u63A5\u8BA9\u7528\u6237\u624B\u52A8\u590D\u5236
                prompt('\u8BF7\u624B\u52A8\u590D\u5236\u4EE5\u4E0B\u94FE\u63A5:', text);
            });
        }

        function deleteNode(nodeId) {
            if (confirm('\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u8282\u70B9\u5417\uFF1F')) {
                const button = event.target;
                button.disabled = true;
                button.textContent = '\u5220\u9664\u4E2D...';

                fetch('/delete-node', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: nodeId })
                }).then(response => {
                    if (response.ok) {
                        showNotification('\u2705 \u8282\u70B9\u5220\u9664\u6210\u529F', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error('\u5220\u9664\u5931\u8D25');
                    }
                }).catch(error => {
                    console.error('\u5220\u9664\u5931\u8D25:', error);
                    showNotification('\u274C \u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5', 'error');
                    button.disabled = false;
                    button.textContent = '\u5220\u9664';
                });
            }
        }

        function showNotification(message, type) {
            // \u521B\u5EFA\u901A\u77E5\u5143\u7D20
            const notification = document.createElement('div');
            notification.className = \`alert alert-\${type === 'success' ? 'success' : 'error'}\`;
            notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            \`;
            notification.textContent = message;

            // \u6DFB\u52A0\u52A8\u753B\u6837\u5F0F
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = \`
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                \`;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 3\u79D2\u540E\u81EA\u52A8\u79FB\u9664
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // \u8868\u5355\u63D0\u4EA4\u589E\u5F3A
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[action="/add-node"]');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const button = form.querySelector('button[type="submit"]');
                    button.disabled = true;
                    button.textContent = '\u6DFB\u52A0\u4E2D...';

                    // \u5982\u679C\u8868\u5355\u63D0\u4EA4\u5931\u8D25\uFF0C\u6062\u590D\u6309\u94AE\u72B6\u6001
                    setTimeout(() => {
                        button.disabled = false;
                        button.textContent = '\u6DFB\u52A0\u8282\u70B9';
                    }, 5000);
                });
            }
        });
    <\/script>
</body>
</html>`;
  }
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-cANl8R/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-cANl8R/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
