{"name": "youch", "version": "3.3.4", "description": "HTML Pretty error stack viewer", "main": "src/Youch.js", "files": ["src", "index.d.ts"], "types": "./index.d.ts", "directories": {"example": "examples"}, "scripts": {"pretest": "npm run lint", "prepublishOnly": "npm run build", "build": "node bin/compile.js", "test": "npm run build && node test/youch.spec.js && node test/youch.spec.mjs", "lint": "standard"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"concat": "^1.0.3", "cz-conventional-changelog": "^3.3.0", "japa": "^4.0.0", "standard": "^17.1.2", "supertest": "^6.3.4", "uglify-js": "^3.19.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"cookie": "^0.7.1", "mustache": "^4.2.0", "stacktracey": "^2.1.8"}, "repository": {"type": "git", "url": "git+https://github.com/poppinss/youch.git"}, "keywords": ["errors", "error-reporting", "whoops"], "bugs": {"url": "https://github.com/poppinss/youch/issues"}, "standard": {"ignore": ["static"]}, "homepage": "https://github.com/poppinss/youch#readme"}