{"clientTcpRtt": 161, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 17622, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "region": "Guangdong", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "Q8HpRO7Bpj8tIjcFyNbyDoz+/ORN6X8GphEgNM+kXsw=", "tlsExportedAuthenticator": {"clientFinished": "55690061bab371767b5cba1bcd53ec317fe0253c21c335f476ddfcd7945f070dfa4eaf62584eb0d8a0a7fd18797bc31e", "clientHandshake": "0a9a697f8d5d870b1e1a84b2fbe2380406a9579d47ed551526487b33c59a9dec6ba3f4f8c29e5b8e184467ff7e5d9586", "serverHandshake": "e7304b15f5789ceb56144d34a6ab9c91c3c19a0599aed30236f884f3c851998c5b538f23378e9fff3b36f0ab518d1a0e", "serverFinished": "78f3845987e87dac554714e2e8beb37bdc018f99e5b4a46439e0bef9f630efcbd280a29ceb1795c54f36d042d9f5fca3"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "113.25390", "latitude": "23.11810", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "city": "Guangzhou", "tlsVersion": "TLSv1.3", "regionCode": "GD", "asOrganization": "China Unicom Guangzhou", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}