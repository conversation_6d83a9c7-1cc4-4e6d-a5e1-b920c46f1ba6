# 自定义域名设置指南

## 📋 设置步骤

### 1. 域名DNS配置
在你的域名管理面板中，添加以下DNS记录：

```
类型: CNAME
名称: loscoy
值: sub-converter.yl1932536551.workers.dev
TTL: 自动或300秒
```

### 2. Cloudflare Workers 域名绑定

#### 方法一：通过Cloudflare Dashboard
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 Workers & Pages
3. 选择你的 `sub-converter` Worker
4. 点击 "Settings" → "Triggers"
5. 在 "Custom Domains" 部分点击 "Add Custom Domain"
6. 输入: `loscoy.ggff.net`
7. 点击 "Add Domain"

#### 方法二：通过命令行
```bash
# 添加自定义域名
wrangler custom-domains add loscoy.ggff.net --worker sub-converter

# 查看域名状态
wrangler custom-domains list
```

### 3. 验证配置
设置完成后，你可以通过以下链接访问：

- **管理面板**: https://loscoy.ggff.net
- **Clash订阅**: https://loscoy.ggff.net/clash
- **V2Ray订阅**: https://loscoy.ggff.net/v2ray
- **Surge订阅**: https://loscoy.ggff.net/surge

### 4. 测试连接
```bash
# 测试域名解析
nslookup loscoy.ggff.net

# 测试HTTP访问
curl -I https://loscoy.ggff.net

# 测试订阅链接
curl https://loscoy.ggff.net/clash
```

## 🔧 故障排除

### 域名无法访问
1. 检查DNS记录是否正确设置
2. 等待DNS传播（可能需要几分钟到几小时）
3. 确认Cloudflare中的自定义域名已正确绑定

### SSL证书问题
- Cloudflare会自动为自定义域名提供SSL证书
- 如果遇到SSL错误，等待几分钟让证书生效

### 订阅链接更新
设置完成后，你的新订阅链接将是：
- Clash: `https://loscoy.ggff.net/clash`
- V2Ray: `https://loscoy.ggff.net/v2ray`
- Surge: `https://loscoy.ggff.net/surge`

## 📝 注意事项

1. **DNS传播时间**: 新的DNS记录可能需要几分钟到几小时才能全球生效
2. **SSL证书**: Cloudflare会自动为你的自定义域名提供免费SSL证书
3. **域名所有权**: 确保你拥有 `ggff.net` 域名的管理权限
4. **备用访问**: 在域名设置期间，你仍可以通过原始Workers域名访问服务

## 🚀 部署命令

```bash
# 部署更新后的配置
npm run deploy

# 或者直接使用wrangler
wrangler deploy
```
