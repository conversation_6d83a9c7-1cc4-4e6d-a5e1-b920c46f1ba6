{"version": 3, "sources": ["../bundle-gDv3kl/checked-fetch.js", "../bundle-gDv3kl/strip-cf-connecting-ip-header.js", "../../../src/auth.js", "../../../src/converter.js", "../../../src/index.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-gDv3kl/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-gDv3kl/middleware-loader.entry.ts"], "sourceRoot": "E:\\Git\\sub_conventer\\.wrangler\\tmp\\dev-Z2zdhF", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "// 认证相关功能\n\nexport class Auth {\n  constructor(env) {\n    this.env = env;\n  }\n\n  // 验证用户凭据\n  validateCredentials(username, password) {\n    return username === this.env.ADMIN_USERNAME && password === this.env.ADMIN_PASSWORD;\n  }\n\n  // 生成JWT token (简化版)\n  generateToken(username) {\n    const payload = {\n      username,\n      exp: Date.now() + (24 * 60 * 60 * 1000) // 24小时过期\n    };\n    return btoa(JSON.stringify(payload));\n  }\n\n  // 验证token\n  validateToken(token) {\n    try {\n      const payload = JSON.parse(atob(token));\n      if (payload.exp < Date.now()) {\n        return false; // token过期\n      }\n      return payload.username === this.env.ADMIN_USERNAME;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  // 从请求中获取token\n  getTokenFromRequest(request) {\n    const cookie = request.headers.get('Cookie');\n    if (!cookie) return null;\n    \n    const match = cookie.match(/auth_token=([^;]+)/);\n    return match ? match[1] : null;\n  }\n\n  // 检查是否已认证\n  isAuthenticated(request) {\n    const token = this.getTokenFromRequest(request);\n    return token && this.validateToken(token);\n  }\n\n  // 创建认证cookie\n  createAuthCookie(token) {\n    return `auth_token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=86400; Path=/`;\n  }\n\n  // 创建登出cookie\n  createLogoutCookie() {\n    return `auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/`;\n  }\n}\n", "// 节点转换器\n\nexport class NodeConverter {\n  constructor() {}\n\n  // 解析节点链接\n  parseNode(config, type) {\n    try {\n      switch (type) {\n        case 'vmess':\n          return this.parseVmess(config);\n        case 'vless':\n          return this.parseVless(config);\n        case 'ss':\n          return this.parseShadowsocks(config);\n        case 'trojan':\n          return this.parseTrojan(config);\n        case 'http':\n          return this.parseHttp(config);\n        default:\n          throw new Error('不支持的节点类型');\n      }\n    } catch (e) {\n      throw new Error(`解析节点失败: ${e.message}`);\n    }\n  }\n\n  // 解析VMess链接\n  parseVmess(config) {\n    if (config.startsWith('vmess://')) {\n      const data = JSON.parse(atob(config.substring(8)));\n      return {\n        type: 'vmess',\n        name: data.ps || 'VMess节点',\n        server: data.add,\n        port: parseInt(data.port),\n        uuid: data.id,\n        alterId: parseInt(data.aid) || 0,\n        cipher: data.scy || 'auto',\n        network: data.net || 'tcp',\n        tls: data.tls === 'tls',\n        path: data.path || '',\n        host: data.host || ''\n      };\n    }\n    throw new Error('无效的VMess链接');\n  }\n\n  // 解析VLess链接\n  parseVless(config) {\n    if (config.startsWith('vless://')) {\n      const url = new URL(config);\n      return {\n        type: 'vless',\n        name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',\n        server: url.hostname,\n        port: parseInt(url.port),\n        uuid: url.username,\n        encryption: url.searchParams.get('encryption') || 'none',\n        network: url.searchParams.get('type') || 'tcp',\n        tls: url.searchParams.get('security') === 'tls',\n        path: url.searchParams.get('path') || '',\n        host: url.searchParams.get('host') || ''\n      };\n    }\n    throw new Error('无效的VLess链接');\n  }\n\n  // 解析Shadowsocks链接\n  parseShadowsocks(config) {\n    if (config.startsWith('ss://')) {\n      const url = new URL(config);\n      const userInfo = atob(url.username);\n      const [method, password] = userInfo.split(':');\n      \n      return {\n        type: 'ss',\n        name: decodeURIComponent(url.hash.substring(1)) || 'SS节点',\n        server: url.hostname,\n        port: parseInt(url.port),\n        method: method,\n        password: password\n      };\n    }\n    throw new Error('无效的Shadowsocks链接');\n  }\n\n  // 解析Trojan链接\n  parseTrojan(config) {\n    if (config.startsWith('trojan://')) {\n      const url = new URL(config);\n      return {\n        type: 'trojan',\n        name: decodeURIComponent(url.hash.substring(1)) || 'Trojan节点',\n        server: url.hostname,\n        port: parseInt(url.port),\n        password: url.username,\n        sni: url.searchParams.get('sni') || url.hostname\n      };\n    }\n    throw new Error('无效的Trojan链接');\n  }\n\n  // 解析HTTP代理\n  parseHttp(config) {\n    if (config.startsWith('http://') || config.startsWith('https://')) {\n      const url = new URL(config);\n      return {\n        type: 'http',\n        name: `HTTP-${url.hostname}`,\n        server: url.hostname,\n        port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),\n        username: url.username || '',\n        password: url.password || '',\n        tls: url.protocol === 'https:'\n      };\n    }\n    throw new Error('无效的HTTP链接');\n  }\n\n  // 转换为V2Ray订阅格式\n  toV2raySubscription(nodes) {\n    const configs = nodes.map(node => {\n      switch (node.type) {\n        case 'vmess':\n          return this.nodeToVmessLink(node);\n        case 'vless':\n          return this.nodeToVlessLink(node);\n        case 'ss':\n          return this.nodeToSsLink(node);\n        case 'trojan':\n          return this.nodeToTrojanLink(node);\n        default:\n          return null;\n      }\n    }).filter(Boolean);\n    \n    return btoa(configs.join('\\n'));\n  }\n\n  // 转换为Clash配置\n  toClashConfig(nodes) {\n    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);\n    const proxyNames = proxies.map(p => p.name);\n    \n    return {\n      port: 7890,\n      'socks-port': 7891,\n      'allow-lan': false,\n      mode: 'rule',\n      'log-level': 'info',\n      'external-controller': '127.0.0.1:9090',\n      proxies: proxies,\n      'proxy-groups': [\n        {\n          name: '🚀 节点选择',\n          type: 'select',\n          proxies: ['♻️ 自动选择', '🎯 全球直连'].concat(proxyNames)\n        },\n        {\n          name: '♻️ 自动选择',\n          type: 'url-test',\n          proxies: proxyNames,\n          url: 'http://www.gstatic.com/generate_204',\n          interval: 300\n        },\n        {\n          name: '🎯 全球直连',\n          type: 'select',\n          proxies: ['DIRECT']\n        }\n      ],\n      rules: [\n        'DOMAIN-SUFFIX,local,DIRECT',\n        'IP-CIDR,*********/8,DIRECT',\n        'IP-CIDR,**********/12,DIRECT',\n        'IP-CIDR,***********/16,DIRECT',\n        'IP-CIDR,10.0.0.0/8,DIRECT',\n        'GEOIP,CN,🎯 全球直连',\n        'MATCH,🚀 节点选择'\n      ]\n    };\n  }\n\n  // 节点转VMess链接\n  nodeToVmessLink(node) {\n    const vmessObj = {\n      v: '2',\n      ps: node.name,\n      add: node.server,\n      port: node.port.toString(),\n      id: node.uuid,\n      aid: node.alterId.toString(),\n      scy: node.cipher,\n      net: node.network,\n      type: 'none',\n      host: node.host,\n      path: node.path,\n      tls: node.tls ? 'tls' : ''\n    };\n    return 'vmess://' + btoa(JSON.stringify(vmessObj));\n  }\n\n  // 节点转VLess链接\n  nodeToVlessLink(node) {\n    const params = new URLSearchParams();\n    params.set('encryption', node.encryption);\n    params.set('type', node.network);\n    if (node.tls) params.set('security', 'tls');\n    if (node.path) params.set('path', node.path);\n    if (node.host) params.set('host', node.host);\n    \n    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转SS链接\n  nodeToSsLink(node) {\n    const userInfo = btoa(`${node.method}:${node.password}`);\n    return `ss://${userInfo}@${node.server}:${node.port}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转Trojan链接\n  nodeToTrojanLink(node) {\n    const params = new URLSearchParams();\n    if (node.sni) params.set('sni', node.sni);\n    \n    return `trojan://${node.password}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;\n  }\n\n  // 节点转Clash代理配置\n  nodeToClashProxy(node) {\n    switch (node.type) {\n      case 'vmess':\n        return {\n          name: node.name,\n          type: 'vmess',\n          server: node.server,\n          port: node.port,\n          uuid: node.uuid,\n          alterId: node.alterId,\n          cipher: node.cipher,\n          network: node.network,\n          tls: node.tls,\n          'ws-opts': node.network === 'ws' ? {\n            path: node.path,\n            headers: node.host ? { Host: node.host } : {}\n          } : undefined\n        };\n      case 'ss':\n        return {\n          name: node.name,\n          type: 'ss',\n          server: node.server,\n          port: node.port,\n          cipher: node.method,\n          password: node.password\n        };\n      case 'trojan':\n        return {\n          name: node.name,\n          type: 'trojan',\n          server: node.server,\n          port: node.port,\n          password: node.password,\n          sni: node.sni\n        };\n      default:\n        return null;\n    }\n  }\n}\n", "import { Auth } from './auth.js';\nimport { NodeConverter } from './converter.js';\n\n// 模板文件内容 (在实际部署时需要读取文件)\nconst templates = {\n  css: `/* CSS content will be loaded here */`,\n  login: `/* Login HTML will be loaded here */`,\n  dashboard: `/* Dashboard HTML will be loaded here */`\n};\n\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n    const auth = new Auth(env);\n    const converter = new NodeConverter();\n\n    // 路由处理\n    switch (url.pathname) {\n      case '/':\n        return this.handleHome(request, auth, env);\n      \n      case '/login':\n        return this.handleLogin(request, auth, env);\n      \n      case '/logout':\n        return this.handleLogout(request, auth);\n      \n      case '/add-node':\n        return this.handleAddNode(request, auth, converter, env);\n      \n      case '/delete-node':\n        return this.handleDeleteNode(request, auth, env);\n      \n      case '/sub/v2ray':\n        return this.handleV2raySubscription(converter, env);\n      \n      case '/sub/clash':\n        return this.handleClashSubscription(converter, env);\n      \n      case '/sub/surge':\n        return this.handleSurgeSubscription(converter, env);\n      \n      default:\n        return new Response('Not Found', { status: 404 });\n    }\n  },\n\n  // 处理首页\n  async handleHome(request, auth, env) {\n    if (!auth.isAuthenticated(request)) {\n      return this.renderLogin();\n    }\n    return this.renderDashboard(request, env);\n  },\n\n  // 处理登录\n  async handleLogin(request, auth, env) {\n    if (request.method === 'GET') {\n      return this.renderLogin();\n    }\n\n    if (request.method === 'POST') {\n      const formData = await request.formData();\n      const username = formData.get('username');\n      const password = formData.get('password');\n\n      if (auth.validateCredentials(username, password)) {\n        const token = auth.generateToken(username);\n        const response = new Response('', {\n          status: 302,\n          headers: {\n            'Location': '/',\n            'Set-Cookie': auth.createAuthCookie(token)\n          }\n        });\n        return response;\n      } else {\n        return this.renderLogin('用户名或密码错误');\n      }\n    }\n  },\n\n  // 处理登出\n  async handleLogout(request, auth) {\n    return new Response('', {\n      status: 302,\n      headers: {\n        'Location': '/',\n        'Set-Cookie': auth.createLogoutCookie()\n      }\n    });\n  },\n\n  // 处理添加节点\n  async handleAddNode(request, auth, converter, env) {\n    if (!auth.isAuthenticated(request)) {\n      return new Response('Unauthorized', { status: 401 });\n    }\n\n    if (request.method === 'POST') {\n      try {\n        const formData = await request.formData();\n        const type = formData.get('type');\n        const name = formData.get('name');\n        const config = formData.get('config');\n\n        // 解析节点\n        const node = converter.parseNode(config, type);\n        node.name = name; // 使用用户提供的名称\n        node.id = Date.now().toString(); // 简单的ID生成\n\n        // 获取现有节点\n        const existingNodes = await this.getNodes(env);\n        existingNodes.push(node);\n\n        // 保存到KV\n        await env.NODES_KV.put('nodes', JSON.stringify(existingNodes));\n\n        return new Response('', {\n          status: 302,\n          headers: { 'Location': '/' }\n        });\n      } catch (e) {\n        return this.renderDashboard(request, env, `添加节点失败: ${e.message}`);\n      }\n    }\n  },\n\n  // 处理删除节点\n  async handleDeleteNode(request, auth, env) {\n    if (!auth.isAuthenticated(request)) {\n      return new Response('Unauthorized', { status: 401 });\n    }\n\n    if (request.method === 'POST') {\n      const { id } = await request.json();\n      const nodes = await this.getNodes(env);\n      const filteredNodes = nodes.filter(node => node.id !== id);\n      await env.NODES_KV.put('nodes', JSON.stringify(filteredNodes));\n      return new Response('OK');\n    }\n  },\n\n  // 处理V2Ray订阅\n  async handleV2raySubscription(converter, env) {\n    const nodes = await this.getNodes(env);\n    const subscription = converter.toV2raySubscription(nodes);\n    \n    return new Response(subscription, {\n      headers: {\n        'Content-Type': 'text/plain; charset=utf-8',\n        'Content-Disposition': 'attachment; filename=\"v2ray.txt\"'\n      }\n    });\n  },\n\n  // 处理Clash订阅\n  async handleClashSubscription(converter, env) {\n    const nodes = await this.getNodes(env);\n    const clashConfig = converter.toClashConfig(nodes);\n    \n    return new Response(JSON.stringify(clashConfig, null, 2), {\n      headers: {\n        'Content-Type': 'application/yaml; charset=utf-8',\n        'Content-Disposition': 'attachment; filename=\"clash.yaml\"'\n      }\n    });\n  },\n\n  // 处理Surge订阅\n  async handleSurgeSubscription(converter, env) {\n    const nodes = await this.getNodes(env);\n    // 简化的Surge配置生成\n    const surgeConfig = this.generateSurgeConfig(nodes);\n    \n    return new Response(surgeConfig, {\n      headers: {\n        'Content-Type': 'text/plain; charset=utf-8',\n        'Content-Disposition': 'attachment; filename=\"surge.conf\"'\n      }\n    });\n  },\n\n  // 获取节点列表\n  async getNodes(env) {\n    try {\n      const nodesData = await env.NODES_KV.get('nodes');\n      return nodesData ? JSON.parse(nodesData) : [];\n    } catch (e) {\n      return [];\n    }\n  },\n\n  // 渲染登录页面\n  renderLogin(errorMessage = '') {\n    const css = this.getCSS();\n    let html = this.getLoginHTML();\n    html = html.replace('{{CSS}}', css);\n    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ? \n      `<div class=\"alert alert-error\">${errorMessage}</div>` : '');\n    \n    return new Response(html, {\n      headers: { 'Content-Type': 'text/html; charset=utf-8' }\n    });\n  },\n\n  // 渲染管理面板\n  async renderDashboard(request, env, errorMessage = '') {\n    const css = this.getCSS();\n    const nodes = await this.getNodes(env);\n    const baseUrl = new URL(request.url).origin;\n    \n    let html = this.getDashboardHTML();\n    html = html.replace('{{CSS}}', css);\n    html = html.replace('{{BASE_URL}}', baseUrl);\n    html = html.replace('{{NODES_LIST}}', this.generateNodesList(nodes));\n    \n    return new Response(html, {\n      headers: { 'Content-Type': 'text/html; charset=utf-8' }\n    });\n  },\n\n  // 生成节点列表HTML\n  generateNodesList(nodes) {\n    if (nodes.length === 0) {\n      return '<p>暂无节点</p>';\n    }\n    \n    return nodes.map(node => `\n      <div class=\"node-item\">\n        <div class=\"node-header\">\n          <span class=\"node-type\">${node.type.toUpperCase()}</span>\n          <button class=\"btn-danger\" onclick=\"deleteNode('${node.id}')\">删除</button>\n        </div>\n        <div class=\"node-details\">\n          <strong>${node.name}</strong><br>\n          ${node.server}:${node.port}\n        </div>\n      </div>\n    `).join('');\n  },\n\n  // 生成Surge配置\n  generateSurgeConfig(nodes) {\n    const proxies = nodes.map(node => {\n      switch (node.type) {\n        case 'ss':\n          return `${node.name} = ss, ${node.server}, ${node.port}, encrypt-method=${node.method}, password=${node.password}`;\n        case 'http':\n          return `${node.name} = http, ${node.server}, ${node.port}${node.username ? `, username=${node.username}, password=${node.password}` : ''}`;\n        default:\n          return null;\n      }\n    }).filter(Boolean);\n\n    return `[General]\nskip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, localhost, *.local\n\n[Proxy]\n${proxies.join('\\n')}\n\n[Proxy Group]\nProxy = select, ${nodes.map(n => n.name).join(', ')}\n\n[Rule]\nFINAL,Proxy`;\n  },\n\n  // 获取CSS内容\n  getCSS() {\n    return `* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.container {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  width: 100%;\n  max-width: 400px;\n}\n\n.dashboard-container {\n  max-width: 1200px;\n  margin: 2rem auto;\n  padding: 0 1rem;\n}\n\nh1, h2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #555;\n  font-weight: 500;\n}\n\ninput, textarea, select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e1e5e9;\n  border-radius: 5px;\n  font-size: 1rem;\n  transition: border-color 0.3s;\n}\n\ninput:focus, textarea:focus, select:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\nbutton {\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\nbutton:hover {\n  transform: translateY(-2px);\n}\n\n.btn-secondary {\n  background: #6c757d;\n  margin-top: 0.5rem;\n}\n\n.btn-danger {\n  background: #dc3545;\n  width: auto;\n  padding: 0.5rem 1rem;\n  margin-left: 0.5rem;\n}\n\n.node-item {\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 5px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n\n.node-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.node-type {\n  background: #007bff;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.8rem;\n}\n\n.node-details {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n.subscription-links {\n  background: #e9ecef;\n  border-radius: 5px;\n  padding: 1rem;\n  margin-top: 2rem;\n}\n\n.link-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n  padding: 0.5rem;\n  background: white;\n  border-radius: 3px;\n}\n\n.copy-btn {\n  background: #28a745;\n  width: auto;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.8rem;\n}\n\n.alert {\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  border-radius: 5px;\n}\n\n.alert-success {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.alert-error {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}`;\n  },\n\n  // 获取登录页面HTML\n  getLoginHTML() {\n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>订阅转换器 - 登录</title>\n    <style>{{CSS}}</style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>订阅转换器</h1>\n        <form method=\"POST\" action=\"/login\">\n            <div class=\"form-group\">\n                <label for=\"username\">用户名:</label>\n                <input type=\"text\" id=\"username\" name=\"username\" required>\n            </div>\n            <div class=\"form-group\">\n                <label for=\"password\">密码:</label>\n                <input type=\"password\" id=\"password\" name=\"password\" required>\n            </div>\n            <button type=\"submit\">登录</button>\n        </form>\n        {{ERROR_MESSAGE}}\n    </div>\n</body>\n</html>`;\n  },\n\n  // 获取管理面板HTML\n  getDashboardHTML() {\n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>订阅转换器 - 管理面板</title>\n    <style>{{CSS}}</style>\n</head>\n<body>\n    <div class=\"dashboard-container\">\n        <h1>节点管理面板</h1>\n\n        <!-- 添加节点表单 -->\n        <div class=\"container\">\n            <h2>添加新节点</h2>\n            <form method=\"POST\" action=\"/add-node\">\n                <div class=\"form-group\">\n                    <label for=\"node-type\">节点类型:</label>\n                    <select id=\"node-type\" name=\"type\" required>\n                        <option value=\"\">选择节点类型</option>\n                        <option value=\"vmess\">VMess</option>\n                        <option value=\"vless\">VLess</option>\n                        <option value=\"ss\">Shadowsocks</option>\n                        <option value=\"trojan\">Trojan</option>\n                        <option value=\"http\">HTTP/HTTPS</option>\n                    </select>\n                </div>\n                <div class=\"form-group\">\n                    <label for=\"node-name\">节点名称:</label>\n                    <input type=\"text\" id=\"node-name\" name=\"name\" required>\n                </div>\n                <div class=\"form-group\">\n                    <label for=\"node-config\">节点配置 (链接或JSON):</label>\n                    <textarea id=\"node-config\" name=\"config\" rows=\"4\" required placeholder=\"粘贴节点链接或配置信息\"></textarea>\n                </div>\n                <button type=\"submit\">添加节点</button>\n            </form>\n        </div>\n\n        <!-- 订阅链接 -->\n        <div class=\"container subscription-links\">\n            <h2>订阅链接</h2>\n            <div class=\"link-item\">\n                <span>V2Ray订阅:</span>\n                <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/v2ray')\">复制</button>\n            </div>\n            <div class=\"link-item\">\n                <span>Clash订阅:</span>\n                <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/clash')\">复制</button>\n            </div>\n            <div class=\"link-item\">\n                <span>Surge订阅:</span>\n                <button class=\"copy-btn\" onclick=\"copyToClipboard('{{BASE_URL}}/sub/surge')\">复制</button>\n            </div>\n        </div>\n\n        <!-- 节点列表 -->\n        <div class=\"container\">\n            <h2>已添加的节点</h2>\n            {{NODES_LIST}}\n        </div>\n\n        <!-- 退出登录 -->\n        <div class=\"container\">\n            <form method=\"POST\" action=\"/logout\">\n                <button type=\"submit\" class=\"btn-secondary\">退出登录</button>\n            </form>\n        </div>\n    </div>\n\n    <script>\n        function copyToClipboard(text) {\n            navigator.clipboard.writeText(text).then(function() {\n                alert('链接已复制到剪贴板');\n            });\n        }\n\n        function deleteNode(nodeId) {\n            if (confirm('确定要删除这个节点吗？')) {\n                fetch('/delete-node', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    },\n                    body: JSON.stringify({ id: nodeId })\n                }).then(() => {\n                    location.reload();\n                });\n            }\n        }\n    </script>\n</body>\n</html>`;\n  }\n};\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\Git\\\\sub_conventer\\\\src\\\\index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"E:\\\\Git\\\\sub_conventer\\\\src\\\\index.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-gDv3kl\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"E:\\\\Git\\\\sub_conventer\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-gDv3kl\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\Git\\\\sub_conventer\\\\.wrangler\\\\tmp\\\\bundle-gDv3kl\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACVM,IAAM,OAAN,MAAW;AAAA,EAChB,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA;AAAA,EAGA,oBAAoB,UAAU,UAAU;AACtC,WAAO,aAAa,KAAK,IAAI,kBAAkB,aAAa,KAAK,IAAI;AAAA,EACvE;AAAA;AAAA,EAGA,cAAc,UAAU;AACtB,UAAM,UAAU;AAAA,MACd;AAAA,MACA,KAAK,KAAK,IAAI,IAAK,KAAK,KAAK,KAAK;AAAA;AAAA,IACpC;AACA,WAAO,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EACrC;AAAA;AAAA,EAGA,cAAc,OAAO;AACnB,QAAI;AACF,YAAM,UAAU,KAAK,MAAM,KAAK,KAAK,CAAC;AACtC,UAAI,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC5B,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,aAAa,KAAK,IAAI;AAAA,IACvC,SAAS,GAAP;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,oBAAoB,SAAS;AAC3B,UAAM,SAAS,QAAQ,QAAQ,IAAI,QAAQ;AAC3C,QAAI,CAAC;AAAQ,aAAO;AAEpB,UAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,WAAO,QAAQ,MAAM,CAAC,IAAI;AAAA,EAC5B;AAAA;AAAA,EAGA,gBAAgB,SAAS;AACvB,UAAM,QAAQ,KAAK,oBAAoB,OAAO;AAC9C,WAAO,SAAS,KAAK,cAAc,KAAK;AAAA,EAC1C;AAAA;AAAA,EAGA,iBAAiB,OAAO;AACtB,WAAO,cAAc;AAAA,EACvB;AAAA;AAAA,EAGA,qBAAqB;AACnB,WAAO;AAAA,EACT;AACF;AAxDa;;;ACAN,IAAM,gBAAN,MAAoB;AAAA,EACzB,cAAc;AAAA,EAAC;AAAA;AAAA,EAGf,UAAU,QAAQ,MAAM;AACtB,QAAI;AACF,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO,KAAK,WAAW,MAAM;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,WAAW,MAAM;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,iBAAiB,MAAM;AAAA,QACrC,KAAK;AACH,iBAAO,KAAK,YAAY,MAAM;AAAA,QAChC,KAAK;AACH,iBAAO,KAAK,UAAU,MAAM;AAAA,QAC9B;AACE,gBAAM,IAAI,MAAM,kDAAU;AAAA,MAC9B;AAAA,IACF,SAAS,GAAP;AACA,YAAM,IAAI,MAAM,yCAAW,EAAE,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAGA,WAAW,QAAQ;AACjB,QAAI,OAAO,WAAW,UAAU,GAAG;AACjC,YAAM,OAAO,KAAK,MAAM,KAAK,OAAO,UAAU,CAAC,CAAC,CAAC;AACjD,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,KAAK,MAAM;AAAA,QACjB,QAAQ,KAAK;AAAA,QACb,MAAM,SAAS,KAAK,IAAI;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,SAAS,SAAS,KAAK,GAAG,KAAK;AAAA,QAC/B,QAAQ,KAAK,OAAO;AAAA,QACpB,SAAS,KAAK,OAAO;AAAA,QACrB,KAAK,KAAK,QAAQ;AAAA,QAClB,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,UAAM,IAAI,MAAM,qCAAY;AAAA,EAC9B;AAAA;AAAA,EAGA,WAAW,QAAQ;AACjB,QAAI,OAAO,WAAW,UAAU,GAAG;AACjC,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,QACnD,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI;AAAA,QACvB,MAAM,IAAI;AAAA,QACV,YAAY,IAAI,aAAa,IAAI,YAAY,KAAK;AAAA,QAClD,SAAS,IAAI,aAAa,IAAI,MAAM,KAAK;AAAA,QACzC,KAAK,IAAI,aAAa,IAAI,UAAU,MAAM;AAAA,QAC1C,MAAM,IAAI,aAAa,IAAI,MAAM,KAAK;AAAA,QACtC,MAAM,IAAI,aAAa,IAAI,MAAM,KAAK;AAAA,MACxC;AAAA,IACF;AACA,UAAM,IAAI,MAAM,qCAAY;AAAA,EAC9B;AAAA;AAAA,EAGA,iBAAiB,QAAQ;AACvB,QAAI,OAAO,WAAW,OAAO,GAAG;AAC9B,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,YAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,YAAM,CAAC,QAAQ,QAAQ,IAAI,SAAS,MAAM,GAAG;AAE7C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,QACnD,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,MAAM,2CAAkB;AAAA,EACpC;AAAA;AAAA,EAGA,YAAY,QAAQ;AAClB,QAAI,OAAO,WAAW,WAAW,GAAG;AAClC,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,mBAAmB,IAAI,KAAK,UAAU,CAAC,CAAC,KAAK;AAAA,QACnD,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI;AAAA,QACvB,UAAU,IAAI;AAAA,QACd,KAAK,IAAI,aAAa,IAAI,KAAK,KAAK,IAAI;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,IAAI,MAAM,sCAAa;AAAA,EAC/B;AAAA;AAAA,EAGA,UAAU,QAAQ;AAChB,QAAI,OAAO,WAAW,SAAS,KAAK,OAAO,WAAW,UAAU,GAAG;AACjE,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,QAAQ,IAAI;AAAA,QAClB,QAAQ,IAAI;AAAA,QACZ,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI,aAAa,WAAW,MAAM;AAAA,QAC/D,UAAU,IAAI,YAAY;AAAA,QAC1B,UAAU,IAAI,YAAY;AAAA,QAC1B,KAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF;AACA,UAAM,IAAI,MAAM,oCAAW;AAAA,EAC7B;AAAA;AAAA,EAGA,oBAAoB,OAAO;AACzB,UAAM,UAAU,MAAM,IAAI,UAAQ;AAChC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC,KAAK;AACH,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC,KAAK;AACH,iBAAO,KAAK,aAAa,IAAI;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,iBAAiB,IAAI;AAAA,QACnC;AACE,iBAAO;AAAA,MACX;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AAEjB,WAAO,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAChC;AAAA;AAAA,EAGA,cAAc,OAAO;AACnB,UAAM,UAAU,MAAM,IAAI,UAAQ,KAAK,iBAAiB,IAAI,CAAC,EAAE,OAAO,OAAO;AAC7E,UAAM,aAAa,QAAQ,IAAI,OAAK,EAAE,IAAI;AAE1C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,MACb,uBAAuB;AAAA,MACvB;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,CAAC,yCAAW,oCAAS,EAAE,OAAO,UAAU;AAAA,QACnD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,CAAC,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,gBAAgB,MAAM;AACpB,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,IAAI,KAAK;AAAA,MACT,KAAK,KAAK;AAAA,MACV,MAAM,KAAK,KAAK,SAAS;AAAA,MACzB,IAAI,KAAK;AAAA,MACT,KAAK,KAAK,QAAQ,SAAS;AAAA,MAC3B,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,KAAK,KAAK,MAAM,QAAQ;AAAA,IAC1B;AACA,WAAO,aAAa,KAAK,KAAK,UAAU,QAAQ,CAAC;AAAA,EACnD;AAAA;AAAA,EAGA,gBAAgB,MAAM;AACpB,UAAM,SAAS,IAAI,gBAAgB;AACnC,WAAO,IAAI,cAAc,KAAK,UAAU;AACxC,WAAO,IAAI,QAAQ,KAAK,OAAO;AAC/B,QAAI,KAAK;AAAK,aAAO,IAAI,YAAY,KAAK;AAC1C,QAAI,KAAK;AAAM,aAAO,IAAI,QAAQ,KAAK,IAAI;AAC3C,QAAI,KAAK;AAAM,aAAO,IAAI,QAAQ,KAAK,IAAI;AAE3C,WAAO,WAAW,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,OAAO,SAAS,KAAK,mBAAmB,KAAK,IAAI;AAAA,EAC9G;AAAA;AAAA,EAGA,aAAa,MAAM;AACjB,UAAM,WAAW,KAAK,GAAG,KAAK,UAAU,KAAK,UAAU;AACvD,WAAO,QAAQ,YAAY,KAAK,UAAU,KAAK,QAAQ,mBAAmB,KAAK,IAAI;AAAA,EACrF;AAAA;AAAA,EAGA,iBAAiB,MAAM;AACrB,UAAM,SAAS,IAAI,gBAAgB;AACnC,QAAI,KAAK;AAAK,aAAO,IAAI,OAAO,KAAK,GAAG;AAExC,WAAO,YAAY,KAAK,YAAY,KAAK,UAAU,KAAK,QAAQ,OAAO,SAAS,KAAK,mBAAmB,KAAK,IAAI;AAAA,EACnH;AAAA;AAAA,EAGA,iBAAiB,MAAM;AACrB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,KAAK,KAAK;AAAA,UACV,WAAW,KAAK,YAAY,OAAO;AAAA,YACjC,MAAM,KAAK;AAAA,YACX,SAAS,KAAK,OAAO,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,UAC9C,IAAI;AAAA,QACN;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,UAAU,KAAK;AAAA,QACjB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,KAAK,KAAK;AAAA,QACZ;AAAA,MACF;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACF;AA5Qa;;;ACQb,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,IAAI,KAAK,GAAG;AACzB,UAAM,YAAY,IAAI,cAAc;AAGpC,YAAQ,IAAI,UAAU;AAAA,MACpB,KAAK;AACH,eAAO,KAAK,WAAW,SAAS,MAAM,GAAG;AAAA,MAE3C,KAAK;AACH,eAAO,KAAK,YAAY,SAAS,MAAM,GAAG;AAAA,MAE5C,KAAK;AACH,eAAO,KAAK,aAAa,SAAS,IAAI;AAAA,MAExC,KAAK;AACH,eAAO,KAAK,cAAc,SAAS,MAAM,WAAW,GAAG;AAAA,MAEzD,KAAK;AACH,eAAO,KAAK,iBAAiB,SAAS,MAAM,GAAG;AAAA,MAEjD,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,GAAG;AAAA,MAEpD,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,GAAG;AAAA,MAEpD,KAAK;AACH,eAAO,KAAK,wBAAwB,WAAW,GAAG;AAAA,MAEpD;AACE,eAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,WAAW,SAAS,MAAM,KAAK;AACnC,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,WAAO,KAAK,gBAAgB,SAAS,GAAG;AAAA,EAC1C;AAAA;AAAA,EAGA,MAAM,YAAY,SAAS,MAAM,KAAK;AACpC,QAAI,QAAQ,WAAW,OAAO;AAC5B,aAAO,KAAK,YAAY;AAAA,IAC1B;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,YAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,YAAM,WAAW,SAAS,IAAI,UAAU;AACxC,YAAM,WAAW,SAAS,IAAI,UAAU;AAExC,UAAI,KAAK,oBAAoB,UAAU,QAAQ,GAAG;AAChD,cAAM,QAAQ,KAAK,cAAc,QAAQ;AACzC,cAAM,WAAW,IAAI,SAAS,IAAI;AAAA,UAChC,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,YAAY;AAAA,YACZ,cAAc,KAAK,iBAAiB,KAAK;AAAA,UAC3C;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,OAAO;AACL,eAAO,KAAK,YAAY,kDAAU;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,aAAa,SAAS,MAAM;AAChC,WAAO,IAAI,SAAS,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,cAAc,KAAK,mBAAmB;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,cAAc,SAAS,MAAM,WAAW,KAAK;AACjD,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,IAAI,SAAS,gBAAgB,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrD;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,UAAI;AACF,cAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,cAAM,OAAO,SAAS,IAAI,MAAM;AAChC,cAAM,OAAO,SAAS,IAAI,MAAM;AAChC,cAAM,SAAS,SAAS,IAAI,QAAQ;AAGpC,cAAM,OAAO,UAAU,UAAU,QAAQ,IAAI;AAC7C,aAAK,OAAO;AACZ,aAAK,KAAK,KAAK,IAAI,EAAE,SAAS;AAG9B,cAAM,gBAAgB,MAAM,KAAK,SAAS,GAAG;AAC7C,sBAAc,KAAK,IAAI;AAGvB,cAAM,IAAI,SAAS,IAAI,SAAS,KAAK,UAAU,aAAa,CAAC;AAE7D,eAAO,IAAI,SAAS,IAAI;AAAA,UACtB,QAAQ;AAAA,UACR,SAAS,EAAE,YAAY,IAAI;AAAA,QAC7B,CAAC;AAAA,MACH,SAAS,GAAP;AACA,eAAO,KAAK,gBAAgB,SAAS,KAAK,yCAAW,EAAE,SAAS;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,iBAAiB,SAAS,MAAM,KAAK;AACzC,QAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,aAAO,IAAI,SAAS,gBAAgB,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrD;AAEA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,YAAM,EAAE,GAAG,IAAI,MAAM,QAAQ,KAAK;AAClC,YAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,YAAM,gBAAgB,MAAM,OAAO,UAAQ,KAAK,OAAO,EAAE;AACzD,YAAM,IAAI,SAAS,IAAI,SAAS,KAAK,UAAU,aAAa,CAAC;AAC7D,aAAO,IAAI,SAAS,IAAI;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK;AAC5C,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,eAAe,UAAU,oBAAoB,KAAK;AAExD,WAAO,IAAI,SAAS,cAAc;AAAA,MAChC,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK;AAC5C,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,cAAc,UAAU,cAAc,KAAK;AAEjD,WAAO,IAAI,SAAS,KAAK,UAAU,aAAa,MAAM,CAAC,GAAG;AAAA,MACxD,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,wBAAwB,WAAW,KAAK;AAC5C,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AAErC,UAAM,cAAc,KAAK,oBAAoB,KAAK;AAElD,WAAO,IAAI,SAAS,aAAa;AAAA,MAC/B,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,SAAS,KAAK;AAClB,QAAI;AACF,YAAM,YAAY,MAAM,IAAI,SAAS,IAAI,OAAO;AAChD,aAAO,YAAY,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,IAC9C,SAAS,GAAP;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGA,YAAY,eAAe,IAAI;AAC7B,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,OAAO,KAAK,aAAa;AAC7B,WAAO,KAAK,QAAQ,WAAW,GAAG;AAClC,WAAO,KAAK,QAAQ,qBAAqB,eACvC,kCAAkC,uBAAuB,EAAE;AAE7D,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,IACxD,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,MAAM,gBAAgB,SAAS,KAAK,eAAe,IAAI;AACrD,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AACrC,UAAM,UAAU,IAAI,IAAI,QAAQ,GAAG,EAAE;AAErC,QAAI,OAAO,KAAK,iBAAiB;AACjC,WAAO,KAAK,QAAQ,WAAW,GAAG;AAClC,WAAO,KAAK,QAAQ,gBAAgB,OAAO;AAC3C,WAAO,KAAK,QAAQ,kBAAkB,KAAK,kBAAkB,KAAK,CAAC;AAEnE,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,SAAS,EAAE,gBAAgB,2BAA2B;AAAA,IACxD,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,kBAAkB,OAAO;AACvB,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI,UAAQ;AAAA;AAAA;AAAA,oCAGO,KAAK,KAAK,YAAY;AAAA,4DACE,KAAK;AAAA;AAAA;AAAA,oBAG7C,KAAK;AAAA,YACb,KAAK,UAAU,KAAK;AAAA;AAAA;AAAA,KAG3B,EAAE,KAAK,EAAE;AAAA,EACZ;AAAA;AAAA,EAGA,oBAAoB,OAAO;AACzB,UAAM,UAAU,MAAM,IAAI,UAAQ;AAChC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,iBAAO,GAAG,KAAK,cAAc,KAAK,WAAW,KAAK,wBAAwB,KAAK,oBAAoB,KAAK;AAAA,QAC1G,KAAK;AACH,iBAAO,GAAG,KAAK,gBAAgB,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW,cAAc,KAAK,sBAAsB,KAAK,aAAa;AAAA,QACxI;AACE,iBAAO;AAAA,MACX;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AAEjB,WAAO;AAAA;AAAA;AAAA;AAAA,EAIT,QAAQ,KAAK,IAAI;AAAA;AAAA;AAAA,kBAGD,MAAM,IAAI,OAAK,EAAE,IAAI,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA;AAAA,EAGA,SAAS;AACP,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8JT;AAAA;AAAA,EAGA,eAAe;AACb,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BT;AAAA;AAAA,EAGA,mBAAmB;AACjB,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6FT;AACF;;;AC1iBA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}